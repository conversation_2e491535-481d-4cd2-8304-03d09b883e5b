name: sam_app
description: "تطبيق SAM - مساعدك ومرشدك الزراعي اليمني الذكي"
publish_to: 'none'
version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  cupertino_icons: ^1.0.2
  firebase_core: ^2.15.0
  firebase_auth: ^4.7.2
  cloud_firestore: ^4.8.4
  firebase_storage: ^11.2.5
  provider: ^6.0.5
  shared_preferences: ^2.2.0
  image_picker: ^1.0.2
  path: ^1.8.3
  intl: ^0.18.0
  http: ^1.1.0
  cached_network_image: ^3.2.3
  flutter_svg: ^2.0.7
  google_maps_flutter: ^2.4.0
  geolocator: ^10.0.0
  permission_handler: ^10.4.3
  flutter_local_notifications: ^15.1.0+1
  url_launcher: ^6.1.12
  connectivity_plus: ^4.0.2
  package_info_plus: ^4.1.0
  flutter_native_splash: ^2.3.2
  flutter_launcher_icons: ^0.13.1
  tflite_flutter: ^0.10.1

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^2.0.0

flutter:
  uses-material-design: true
  assets:
    - assets/images/
    - assets/icons/
    - assets/ml_models/
  fonts:
    - family: Tajawal
      fonts:
        - asset: assets/fonts/Tajawal-Regular.ttf
        - asset: assets/fonts/Tajawal-Bold.ttf
          weight: 700
        - asset: assets/fonts/Tajawal-Medium.ttf
          weight: 500
        - asset: assets/fonts/Tajawal-Light.ttf
          weight: 300

flutter_native_splash:
  color: "#2E7D32"
  image: assets/images/splash_logo.png
  android: true
  ios: true

flutter_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/icons/app_icon.png"
