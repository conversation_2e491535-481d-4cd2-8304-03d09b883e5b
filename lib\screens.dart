// ملف الشاشات (Screens) - يحتوي على جميع واجهات المستخدم في التطبيق

import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:image_picker/image_picker.dart';
import 'package:provider/provider.dart';
import 'models.dart';
import 'services.dart';

// ======================================================
// شاشة البداية (Splash Screen)
// ======================================================

class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _navigateToNextScreen();
  }

  Future<void> _navigateToNextScreen() async {
    // تأخير لمدة ثانيتين لمحاكاة شاشة البداية
    await Future.delayed(const Duration(seconds: 2));

    // التحقق من حالة المصادقة
    final authService = Provider.of<AuthService>(context, listen: false);
    final user = await authService.getCurrentUser();

    if (user != null) {
      // إذا كان المستخدم مسجلاً الدخول، انتقل إلى الشاشة الرئيسية
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (_) => HomeScreen(user: user)),
      );
    } else {
      // إذا لم يكن المستخدم مسجلاً الدخول، انتقل إلى شاشة تسجيل الدخول
      Navigator.of(context).pushReplacement(
        MaterialPageRoute(builder: (_) => const LoginScreen()),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            FlutterLogo(size: 100),
            SizedBox(height: 20),
            Text(
              'SAM - مساعدك ومرشدك الزراعي',
              style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 10),
            CircularProgressIndicator(),
          ],
        ),
      ),
    );
  }
}

// ======================================================
// شاشة تسجيل الدخول (Login Screen)
// ======================================================

class LoginScreen extends StatefulWidget {
  const LoginScreen({super.key});

  @override
  State<LoginScreen> createState() => _LoginScreenState();
}

class _LoginScreenState extends State<LoginScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  bool _isLoading = false;

  Future<void> _login() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        final authService = Provider.of<AuthService>(context, listen: false);
        final user = await authService.signInWithEmailAndPassword(
          _emailController.text.trim(),
          _passwordController.text.trim(),
        );
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => HomeScreen(user: user)),
        );
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(e.toString())),
        );
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('تسجيل الدخول')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TextFormField(
                controller: _emailController,
                decoration: const InputDecoration(labelText: 'البريد الإلكتروني'),
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'الرجاء إدخال البريد الإلكتروني';
                  }
                  if (!value.contains('@')) {
                    return 'الرجاء إدخال بريد إلكتروني صالح';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _passwordController,
                decoration: const InputDecoration(labelText: 'كلمة المرور'),
                obscureText: true,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'الرجاء إدخال كلمة المرور';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),
              _isLoading
                  ? const CircularProgressIndicator()
                  : ElevatedButton(
                      onPressed: _login,
                      child: const Text('تسجيل الدخول'),
                    ),
              TextButton(
                onPressed: () {
                  Navigator.of(context).push(
                    MaterialPageRoute(builder: (_) => const RegisterScreen()),
                  );
                },
                child: const Text('ليس لديك حساب؟ إنشاء حساب جديد'),
              ),
              TextButton(
                onPressed: () {
                   Navigator.of(context).push(
                    MaterialPageRoute(builder: (_) => const ForgotPasswordScreen()),
                  );
                },
                child: const Text('نسيت كلمة المرور؟'),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// ======================================================
// شاشة إنشاء حساب جديد (Register Screen)
// ======================================================

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _phoneController = TextEditingController();
  String _userType = 'مزارع'; // القيمة الافتراضية
  bool _isLoading = false;

  Future<void> _register() async {
    if (_formKey.currentState!.validate()) {
      if (_passwordController.text != _confirmPasswordController.text) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('كلمتا المرور غير متطابقتين')),
        );
        return;
      }

      setState(() {
        _isLoading = true;
      });

      try {
        final authService = Provider.of<AuthService>(context, listen: false);
        final user = await authService.signUpWithEmailAndPassword(
          _emailController.text.trim(),
          _passwordController.text.trim(),
          _nameController.text.trim(),
          _phoneController.text.trim(),
          _userType,
        );
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (_) => HomeScreen(user: user)),
        );
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(e.toString())),
        );
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('إنشاء حساب جديد')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: ListView(
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(labelText: 'الاسم الكامل'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'الرجاء إدخال الاسم الكامل';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _emailController,
                decoration: const InputDecoration(labelText: 'البريد الإلكتروني'),
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'الرجاء إدخال البريد الإلكتروني';
                  }
                  if (!value.contains('@')) {
                    return 'الرجاء إدخال بريد إلكتروني صالح';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _passwordController,
                decoration: const InputDecoration(labelText: 'كلمة المرور'),
                obscureText: true,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'الرجاء إدخال كلمة المرور';
                  }
                  if (value.length < 6) {
                    return 'يجب أن تكون كلمة المرور 6 أحرف على الأقل';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _confirmPasswordController,
                decoration: const InputDecoration(labelText: 'تأكيد كلمة المرور'),
                obscureText: true,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'الرجاء تأكيد كلمة المرور';
                  }
                  if (value != _passwordController.text) {
                    return 'كلمتا المرور غير متطابقتين';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _phoneController,
                decoration: const InputDecoration(labelText: 'رقم الهاتف (اختياري)'),
                keyboardType: TextInputType.phone,
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _userType,
                decoration: const InputDecoration(labelText: 'نوع المستخدم'),
                items: ['مزارع', 'مرشد زراعي', 'تاجر'].map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    _userType = newValue!;
                  });
                },
              ),
              const SizedBox(height: 24),
              _isLoading
                  ? const CircularProgressIndicator()
                  : ElevatedButton(
                      onPressed: _register,
                      child: const Text('إنشاء الحساب'),
                    ),
            ],
          ),
        ),
      ),
    );
  }
}

// ======================================================
// شاشة نسيت كلمة المرور (Forgot Password Screen)
// ======================================================

class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _emailController = TextEditingController();
  bool _isLoading = false;

  Future<void> _resetPassword() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      try {
        final authService = Provider.of<AuthService>(context, listen: false);
        await authService.resetPassword(_emailController.text.trim());
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني')),
        );
        Navigator.of(context).pop();
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(e.toString())),
        );
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('نسيت كلمة المرور')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TextFormField(
                controller: _emailController,
                decoration: const InputDecoration(labelText: 'البريد الإلكتروني'),
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'الرجاء إدخال البريد الإلكتروني';
                  }
                  if (!value.contains('@')) {
                    return 'الرجاء إدخال بريد إلكتروني صالح';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 24),
              _isLoading
                  ? const CircularProgressIndicator()
                  : ElevatedButton(
                      onPressed: _resetPassword,
                      child: const Text('إعادة تعيين كلمة المرور'),
                    ),
            ],
          ),
        ),
      ),
    );
  }
}

// ======================================================
// الشاشة الرئيسية (Home Screen)
// ======================================================

class HomeScreen extends StatefulWidget {
  final UserModel user;
  const HomeScreen({super.key, required this.user});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  int _selectedIndex = 0;

  late final List<Widget> _widgetOptions;

 @override
  void initState() {
    super.initState();
    _widgetOptions = <Widget>[
      DashboardScreen(user: widget.user),
      IrrigationSystemsScreen(userId: widget.user.id),
      PlantDiseaseDiagnosisScreen(userId: widget.user.id),
      MarketScreen(userId: widget.user.id),
      ChatListScreen(userId: widget.user.id),
    ];
  }

  void _onItemTapped(int index) {
    setState(() {
      _selectedIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('SAM - مساعدك الزراعي'),
        actions: [
          IconButton(
            icon: const Icon(Icons.person),
            onPressed: () {
              Navigator.of(context).push(MaterialPageRoute(
                builder: (_) => ProfileScreen(user: widget.user),
              ));
            },
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () {
              Navigator.of(context).push(MaterialPageRoute(
                builder: (_) => const SettingsScreen(),
              ));
            },
          ),
        ],
      ),
      body: Center(
        child: _widgetOptions.elementAt(_selectedIndex),
      ),
      bottomNavigationBar: BottomNavigationBar(
        items: const <BottomNavigationBarItem>[
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'الرئيسية',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.water_drop),
            label: 'الري',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.local_florist),
            label: 'الأمراض',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.store),
            label: 'السوق',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.chat),
            label: 'المحادثات',
          ),
        ],
        currentIndex: _selectedIndex,
        selectedItemColor: Theme.of(context).primaryColor,
        unselectedItemColor: Colors.grey,
        onTap: _onItemTapped,
        type: BottomNavigationBarType.fixed, // لإظهار جميع العناوين
      ),
    );
  }
}

// ======================================================
// شاشة لوحة التحكم (Dashboard Screen)
// ======================================================

class DashboardScreen extends StatelessWidget {
  final UserModel user;
  const DashboardScreen({super.key, required this.user});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text('مرحباً بك، ${user.name}!', style: Theme.of(context).textTheme.headlineSmall),
          const SizedBox(height: 20),
          // هنا يمكن إضافة ملخصات للأنظمة المتصلة وحالتها
          // على سبيل المثال، عدد أنظمة الري، آخر تشخيص لمرض، إلخ.
          _buildDashboardCard(
            context,
            title: 'نظام الري الذكي',
            icon: Icons.water_drop,
            onTap: () {
              // الانتقال إلى شاشة نظام الري
              // يمكن استخدام DefaultTabController أو تعديل _selectedIndex في HomeScreen
            },
          ),
          _buildDashboardCard(
            context,
            title: 'تشخيص أمراض النباتات',
            icon: Icons.local_florist,
            onTap: () {
              // الانتقال إلى شاشة تشخيص الأمراض
            },
          ),
          _buildDashboardCard(
            context,
            title: 'السوق الزراعي',
            icon: Icons.store,
            onTap: () {
              // الانتقال إلى شاشة السوق الزراعي
            },
          ),
          _buildDashboardCard(
            context,
            title: 'المحادثات',
            icon: Icons.chat,
            onTap: () {
              // الانتقال إلى شاشة المحادثات
            },
          ),
          const SizedBox(height: 20),
          Text('نصائح زراعية سريعة:', style: Theme.of(context).textTheme.titleLarge),
          const SizedBox(height: 10),
          _buildTipCard(context, 'تأكد من فحص رطوبة التربة بانتظام قبل الري.'),
          _buildTipCard(context, 'استخدم الأسمدة العضوية لتحسين خصوبة التربة.'),
          _buildTipCard(context, 'راقب نباتاتك بحثًا عن أي علامات مبكرة للأمراض أو الآفات.'),
        ],
      ),
    );
  }

  Widget _buildDashboardCard(BuildContext context, {required String title, required IconData icon, required VoidCallback onTap}) {
    return Card(
      elevation: 2,
      margin: const EdgeInsets.symmetric(vertical: 8),
      child: ListTile(
        leading: Icon(icon, size: 40, color: Theme.of(context).primaryColor),
        title: Text(title, style: Theme.of(context).textTheme.titleMedium),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: onTap,
      ),
    );
  }

  Widget _buildTipCard(BuildContext context, String tip) {
    return Card(
      elevation: 1,
      margin: const EdgeInsets.symmetric(vertical: 4),
      color: Theme.of(context).primaryColorLight.withOpacity(0.3),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Text(tip, style: Theme.of(context).textTheme.bodyMedium),
      ),
    );
  }
}

// ======================================================
// شاشة أنظمة الري (Irrigation Systems Screen)
// ======================================================

class IrrigationSystemsScreen extends StatelessWidget {
  final String userId;
  const IrrigationSystemsScreen({super.key, required this.userId});

  @override
  Widget build(BuildContext context) {
    final irrigationService = IrrigationService(userId: userId);
    return Scaffold(
      body: StreamBuilder<List<IrrigationSystemModel>>(
        stream: irrigationService.getIrrigationSystems(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return Center(child: Text('خطأ: ${snapshot.error}'));
          }
          if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(child: Text('لا توجد أنظمة ري مضافة حاليًا.'));
          }

          final systems = snapshot.data!;
          return ListView.builder(
            itemCount: systems.length,
            itemBuilder: (context, index) {
              final system = systems[index];
              return Card(
                margin: const EdgeInsets.all(8.0),
                child: ListTile(
                  title: Text(system.name, style: const TextStyle(fontWeight: FontWeight.bold)),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('معرف ESP: ${system.espId}'),
                      Text('الحالة: ${system.isPumpOn ? "المضخة تعمل" : "المضخة متوقفة"}'),
                      Text('الوضع: ${system.isAutomatic ? "تلقائي" : "يدوي"}'),
                      if (system.sensorData != null)
                        Text('رطوبة التربة: ${system.sensorData!.soilMoisture}% | الحرارة: ${system.sensorData!.temperature}°C | مستوى الماء: ${system.sensorData!.waterLevel}%'),
                    ],
                  ),
                  trailing: const Icon(Icons.arrow_forward_ios),
                  onTap: () {
                    Navigator.of(context).push(MaterialPageRoute(
                      builder: (_) => IrrigationSystemDetailsScreen(systemId: system.id, userId: userId),
                    ));
                  },
                ),
              );
            },
          );
        },
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.of(context).push(MaterialPageRoute(
            builder: (_) => AddEditIrrigationSystemScreen(userId: userId),
          ));
        },
        child: const Icon(Icons.add),
        tooltip: 'إضافة نظام ري جديد',
      ),
    );
  }
}

// ======================================================
// شاشة تفاصيل نظام الري (Irrigation System Details Screen)
// ======================================================

class IrrigationSystemDetailsScreen extends StatelessWidget {
  final String systemId;
  final String userId;
  const IrrigationSystemDetailsScreen({super.key, required this.systemId, required this.userId});

  @override
  Widget build(BuildContext context) {
    final irrigationService = IrrigationService(userId: userId);
    return Scaffold(
      appBar: AppBar(title: const Text('تفاصيل نظام الري')),
      body: StreamBuilder<IrrigationSystemModel>(
        stream: irrigationService.getIrrigationSystem(systemId),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return Center(child: Text('خطأ: ${snapshot.error}'));
          }
          if (!snapshot.hasData) {
            return const Center(child: Text('لم يتم العثور على بيانات النظام.'));
          }

          final system = snapshot.data!;
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('اسم النظام: ${system.name}', style: Theme.of(context).textTheme.titleLarge),
                const SizedBox(height: 10),
                Text('معرف ESP: ${system.espId}'),
                const SizedBox(height: 10),
                Text('عتبة الرطوبة: ${system.moistureThreshold}%'),
                const SizedBox(height: 10),
                Text('مدة الري: ${system.wateringDurationMinutes} دقيقة'),
                const SizedBox(height: 20),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text('تشغيل المضخة:'),
                    Switch(
                      value: system.isPumpOn,
                      onChanged: (value) {
                        irrigationService.togglePump(systemId, value);
                      },
                    ),
                  ],
                ),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    const Text('الوضع التلقائي:'),
                    Switch(
                      value: system.isAutomatic,
                      onChanged: (value) {
                        irrigationService.toggleAutomaticMode(systemId, value);
                      },
                    ),
                  ],
                ),
                const SizedBox(height: 20),
                if (system.sensorData != null)
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text('بيانات الحساسات الأخيرة:', style: Theme.of(context).textTheme.titleMedium),
                          const SizedBox(height: 8),
                          Text('درجة الحرارة: ${system.sensorData!.temperature}°C'),
                          Text('رطوبة التربة: ${system.sensorData!.soilMoisture}%'),
                          Text('مستوى الماء: ${system.sensorData!.waterLevel}%'),
                          Text('آخر تحديث: ${system.sensorData!.timestamp.toDate()}'),
                        ],
                      ),
                    ),
                  ),
                const Spacer(),
                ElevatedButton.icon(
                  onPressed: () {
                    Navigator.of(context).push(MaterialPageRoute(
                      builder: (_) => AddEditIrrigationSystemScreen(userId: userId, system: system),
                    ));
                  },
                  icon: const Icon(Icons.edit),
                  label: const Text('تعديل النظام'),
                  style: ElevatedButton.styleFrom(minimumSize: const Size(double.infinity, 50)),
                ),
                const SizedBox(height: 10),
                ElevatedButton.icon(
                  onPressed: () async {
                    final confirmDelete = await showDialog<bool>(
                      context: context,
                      builder: (context) => AlertDialog(
                        title: const Text('تأكيد الحذف'),
                        content: const Text('هل أنت متأكد أنك تريد حذف هذا النظام؟'),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(false),
                            child: const Text('إلغاء'),
                          ),
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(true),
                            child: const Text('حذف'),
                          ),
                        ],
                      ),
                    );
                    if (confirmDelete == true) {
                      await irrigationService.deleteIrrigationSystem(systemId);
                      Navigator.of(context).pop();
                    }
                  },
                  icon: const Icon(Icons.delete, color: Colors.white),
                  label: const Text('حذف النظام', style: TextStyle(color: Colors.white)),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.red,
                    minimumSize: const Size(double.infinity, 50),
                  ),
                ),
              ],
            ),
          );
        },
      ),
    );
  }
}

// ======================================================
// شاشة إضافة/تعديل نظام الري (Add/Edit Irrigation System Screen)
// ======================================================

class AddEditIrrigationSystemScreen extends StatefulWidget {
  final String userId;
  final IrrigationSystemModel? system;
  const AddEditIrrigationSystemScreen({super.key, required this.userId, this.system});

  @override
  State<AddEditIrrigationSystemScreen> createState() => _AddEditIrrigationSystemScreenState();
}

class _AddEditIrrigationSystemScreenState extends State<AddEditIrrigationSystemScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _espIdController;
  late TextEditingController _moistureThresholdController;
  late TextEditingController _wateringDurationController;
  bool _isAutomatic = false;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.system?.name ?? '');
    _espIdController = TextEditingController(text: widget.system?.espId ?? '');
    _moistureThresholdController = TextEditingController(text: widget.system?.moistureThreshold.toString() ?? '50');
    _wateringDurationController = TextEditingController(text: widget.system?.wateringDurationMinutes.toString() ?? '10');
    _isAutomatic = widget.system?.isAutomatic ?? false;
  }

  Future<void> _saveSystem() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      final irrigationService = IrrigationService(userId: widget.userId);
      final systemData = IrrigationSystemModel(
        id: widget.system?.id ?? '', // سيتم تجاهله في حالة الإضافة
        userId: widget.userId,
        name: _nameController.text.trim(),
        espId: _espIdController.text.trim(),
        isAutomatic: _isAutomatic,
        moistureThreshold: int.parse(_moistureThresholdController.text.trim()),
        wateringDurationMinutes: int.parse(_wateringDurationController.text.trim()),
        isPumpOn: widget.system?.isPumpOn ?? false, // الحفاظ على الحالة الحالية للمضخة
      );

      try {
        if (widget.system == null) {
          // إضافة نظام جديد
          await irrigationService.addIrrigationSystem(systemData);
        } else {
          // تعديل نظام موجود
          await irrigationService.updateIrrigationSystem(systemData.copyWith(id: widget.system!.id));
        }
        Navigator.of(context).pop(); // العودة إلى الشاشة السابقة
        if (widget.system == null) Navigator.of(context).pop(); // العودة مرتين في حالة الإضافة

      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل حفظ النظام: ${e.toString()}')),
        );
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(widget.system == null ? 'إضافة نظام ري جديد' : 'تعديل نظام الري')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: ListView(
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(labelText: 'اسم النظام'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'الرجاء إدخال اسم النظام';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _espIdController,
                decoration: const InputDecoration(labelText: 'معرف ESP32'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'الرجاء إدخال معرف ESP32';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _moistureThresholdController,
                decoration: const InputDecoration(labelText: 'عتبة الرطوبة (%)'),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'الرجاء إدخال عتبة الرطوبة';
                  }
                  final threshold = int.tryParse(value);
                  if (threshold == null || threshold < 0 || threshold > 100) {
                    return 'الرجاء إدخال قيمة بين 0 و 100';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _wateringDurationController,
                decoration: const InputDecoration(labelText: 'مدة الري (دقائق)'),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'الرجاء إدخال مدة الري';
                  }
                  final duration = int.tryParse(value);
                  if (duration == null || duration <= 0) {
                    return 'الرجاء إدخال مدة صالحة';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              SwitchListTile(
                title: const Text('تفعيل الوضع التلقائي'),
                value: _isAutomatic,
                onChanged: (bool value) {
                  setState(() {
                    _isAutomatic = value;
                  });
                },
              ),
              const SizedBox(height: 24),
              _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : ElevatedButton(
                      onPressed: _saveSystem,
                      child: const Text('حفظ النظام'),
                      style: ElevatedButton.styleFrom(minimumSize: const Size(double.infinity, 50)),
                    ),
            ],
          ),
        ),
      ),
    );
  }
}

// ======================================================
// شاشة تشخيص أمراض النباتات (Plant Disease Diagnosis Screen)
// ======================================================

class PlantDiseaseDiagnosisScreen extends StatefulWidget {
  final String userId;
  const PlantDiseaseDiagnosisScreen({super.key, required this.userId});

  @override
  State<PlantDiseaseDiagnosisScreen> createState() => _PlantDiseaseDiagnosisScreenState();
}

class _PlantDiseaseDiagnosisScreenState extends State<PlantDiseaseDiagnosisScreen> {
  File? _imageFile;
  DiseaseDiagnosisModel? _diagnosisResult;
  bool _isLoading = false;
  final ImageService _imageService = ImageService();

  Future<void> _pickImage(ImageSource source) async {
    final pickedFile = source == ImageSource.camera
        ? await _imageService.captureImageFromCamera()
        : await _imageService.pickImageFromGallery();

    if (pickedFile != null) {
      setState(() {
        _imageFile = pickedFile;
        _diagnosisResult = null; // مسح النتيجة السابقة عند اختيار صورة جديدة
      });
    }
  }

  Future<void> _diagnoseDisease() async {
    if (_imageFile == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('الرجاء اختيار صورة أولاً')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final diagnosisService = DiseaseDiagnosisService(userId: widget.userId);
      final result = await diagnosisService.diagnosePlantDisease(_imageFile!);
      setState(() {
        _diagnosisResult = result;
      });
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('فشل تشخيص المرض: ${e.toString()}')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            if (_imageFile != null)
              Image.file(_imageFile!, height: 250, fit: BoxFit.cover),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                ElevatedButton.icon(
                  onPressed: () => _pickImage(ImageSource.camera),
                  icon: const Icon(Icons.camera_alt),
                  label: const Text('الكاميرا'),
                ),
                ElevatedButton.icon(
                  onPressed: () => _pickImage(ImageSource.gallery),
                  icon: const Icon(Icons.photo_library),
                  label: const Text('المعرض'),
                ),
              ],
            ),
            const SizedBox(height: 24),
            if (_imageFile != null)
              _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : ElevatedButton.icon(
                      onPressed: _diagnoseDisease,
                      icon: const Icon(Icons.search),
                      label: const Text('تشخيص المرض'),
                      style: ElevatedButton.styleFrom(padding: const EdgeInsets.symmetric(vertical: 16)),
                    ),
            const SizedBox(height: 24),
            if (_diagnosisResult != null)
              Card(
                elevation: 2,
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('نتائج التشخيص:', style: Theme.of(context).textTheme.titleLarge),
                      const SizedBox(height: 10),
                      Text('اسم المرض: ${_diagnosisResult!.diseaseName}', style: Theme.of(context).textTheme.titleMedium),
                      Text('نسبة الثقة: ${(_diagnosisResult!.confidence * 100).toStringAsFixed(1)}%'),
                      const SizedBox(height: 10),
                      Text('التوصيات:', style: Theme.of(context).textTheme.titleMedium),
                      Text(_diagnosisResult!.recommendations),
                      if (_diagnosisResult!.imageUrl != null)
                        Padding(
                          padding: const EdgeInsets.only(top: 10.0),
                          child: Image.network(_diagnosisResult!.imageUrl!, height: 150, fit: BoxFit.cover),
                        ),
                    ],
                  ),
                ),
              ),
            const SizedBox(height: 20),
            ElevatedButton.icon(
              onPressed: () {
                Navigator.of(context).push(MaterialPageRoute(
                  builder: (_) => DiagnosisHistoryScreen(userId: widget.userId),
                ));
              },
              icon: const Icon(Icons.history),
              label: const Text('عرض سجل التشخيصات'),
            ),
          ],
        ),
      ),
    );
  }
}

// ======================================================
// شاشة سجل تشخيصات الأمراض (Diagnosis History Screen)
// ======================================================

class DiagnosisHistoryScreen extends StatelessWidget {
  final String userId;
  const DiagnosisHistoryScreen({super.key, required this.userId});

  @override
  Widget build(BuildContext context) {
    final diagnosisService = DiseaseDiagnosisService(userId: userId);
    return Scaffold(
      appBar: AppBar(title: const Text('سجل تشخيصات الأمراض')),
      body: StreamBuilder<List<DiseaseDiagnosisModel>>(
        stream: diagnosisService.getDiagnosisHistory(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return Center(child: Text('خطأ: ${snapshot.error}'));
          }
          if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(child: Text('لا يوجد سجل تشخيصات حاليًا.'));
          }

          final history = snapshot.data!;
          return ListView.builder(
            itemCount: history.length,
            itemBuilder: (context, index) {
              final diagnosis = history[index];
              return Card(
                margin: const EdgeInsets.all(8.0),
                child: ListTile(
                  leading: diagnosis.imageUrl != null
                      ? Image.network(diagnosis.imageUrl!, width: 50, height: 50, fit: BoxFit.cover)
                      : const Icon(Icons.image_not_supported, size: 50),
                  title: Text(diagnosis.diseaseName, style: const TextStyle(fontWeight: FontWeight.bold)),
                  subtitle: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('الثقة: ${(diagnosis.confidence * 100).toStringAsFixed(1)}%'),
                      Text('التاريخ: ${diagnosis.timestamp.toDate()}'),
                    ],
                  ),
                  onTap: () {
                    // يمكن عرض تفاصيل التشخيص هنا
                    showDialog(
                      context: context,
                      builder: (context) => AlertDialog(
                        title: Text(diagnosis.diseaseName),
                        content: SingleChildScrollView(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              if (diagnosis.imageUrl != null)
                                Image.network(diagnosis.imageUrl!),
                              const SizedBox(height: 10),
                              Text('الثقة: ${(diagnosis.confidence * 100).toStringAsFixed(1)}%'),
                              const SizedBox(height: 10),
                              Text('التوصيات: ${diagnosis.recommendations}'),
                              const SizedBox(height: 10),
                              Text('التاريخ: ${diagnosis.timestamp.toDate()}'),
                            ],
                          ),
                        ),
                        actions: [
                          TextButton(
                            onPressed: () => Navigator.of(context).pop(),
                            child: const Text('إغلاق'),
                          ),
                        ],
                      ),
                    );
                  },
                ),
              );
            },
          );
        },
      ),
    );
  }
}

// ======================================================
// شاشة السوق الزراعي (Market Screen)
// ======================================================

class MarketScreen extends StatefulWidget {
  final String userId;
  const MarketScreen({super.key, required this.userId});

  @override
  State<MarketScreen> createState() => _MarketScreenState();
}

class _MarketScreenState extends State<MarketScreen> {
  String? _selectedCategory;
  final TextEditingController _searchController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    final marketService = MarketService(userId: widget.userId);
    final List<String> categories = ['الكل', 'خضروات', 'فواكه', 'حبوب', 'أسمدة', 'مبيدات', 'أدوات زراعية'];

    return Scaffold(
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'ابحث عن منتج...',
                      prefixIcon: const Icon(Icons.search),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(25.0),
                        borderSide: BorderSide.none,
                      ),
                      filled: true,
                      fillColor: Colors.grey[200],
                    ),
                    onChanged: (value) {
                      setState(() {}); // لإعادة بناء الواجهة عند تغيير البحث
                    },
                  ),
                ),
                const SizedBox(width: 8),
                DropdownButton<String>(
                  hint: const Text('اختر فئة'),
                  value: _selectedCategory ?? 'الكل',
                  items: categories.map((String category) {
                    return DropdownMenuItem<String>(
                      value: category,
                      child: Text(category),
                    );
                  }).toList(),
                  onChanged: (String? newValue) {
                    setState(() {
                      _selectedCategory = newValue;
                    });
                  },
                ),
              ],
            ),
          ),
          Expanded(
            child: StreamBuilder<List<ProductModel>>(
              stream: marketService.getProducts(
                category: _selectedCategory,
                searchQuery: _searchController.text.trim(),
              ),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }
                if (snapshot.hasError) {
                  return Center(child: Text('خطأ: ${snapshot.error}'));
                }
                if (!snapshot.hasData || snapshot.data!.isEmpty) {
                  return const Center(child: Text('لا توجد منتجات متاحة حاليًا.'));
                }

                final products = snapshot.data!;
                return GridView.builder(
                  padding: const EdgeInsets.all(8.0),
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    childAspectRatio: 0.75, // تعديل نسبة العرض إلى الارتفاع
                    crossAxisSpacing: 8.0,
                    mainAxisSpacing: 8.0,
                  ),
                  itemCount: products.length,
                  itemBuilder: (context, index) {
                    final product = products[index];
                    return Card(
                      elevation: 2,
                      clipBehavior: Clip.antiAlias, // لقص الصورة بشكل صحيح
                      child: InkWell(
                        onTap: () {
                          Navigator.of(context).push(MaterialPageRoute(
                            builder: (_) => ProductDetailsScreen(productId: product.id, userId: widget.userId),
                          ));
                        },
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.stretch,
                          children: [
                            Expanded(
                              flex: 3,
                              child: product.imageUrl != null
                                  ? Image.network(product.imageUrl!, fit: BoxFit.cover)
                                  : Container(color: Colors.grey[300], child: const Icon(Icons.image, size: 50, color: Colors.grey)),
                            ),
                            Expanded(
                              flex: 2,
                              child: Padding(
                                padding: const EdgeInsets.all(8.0),
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(product.name, style: const TextStyle(fontWeight: FontWeight.bold), maxLines: 1, overflow: TextOverflow.ellipsis),
                                    Text('${product.price} ريال يمني', style: TextStyle(color: Theme.of(context).primaryColor, fontWeight: FontWeight.bold)),
                                    if (product.sellerName != null)
                                      Text('البائع: ${product.sellerName}', style: Theme.of(context).textTheme.bodySmall, maxLines: 1, overflow: TextOverflow.ellipsis),
                                  ],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.of(context).push(MaterialPageRoute(
            builder: (_) => AddEditProductScreen(userId: widget.userId),
          ));
        },
        child: const Icon(Icons.add_shopping_cart),
        tooltip: 'إضافة منتج جديد',
      ),
    );
  }
}

// ======================================================
// شاشة تفاصيل المنتج (Product Details Screen)
// ======================================================

class ProductDetailsScreen extends StatelessWidget {
  final String productId;
  final String userId;
  const ProductDetailsScreen({super.key, required this.productId, required this.userId});

  @override
  Widget build(BuildContext context) {
    final marketService = MarketService(userId: userId);
    return Scaffold(
      appBar: AppBar(title: const Text('تفاصيل المنتج')),
      body: FutureBuilder<ProductModel>(
        future: marketService.getProduct(productId),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return Center(child: Text('خطأ: ${snapshot.error}'));
          }
          if (!snapshot.hasData) {
            return const Center(child: Text('لم يتم العثور على بيانات المنتج.'));
          }

          final product = snapshot.data!;
          return SingleChildScrollView(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.stretch,
              children: [
                if (product.imageUrl != null)
                  Image.network(product.imageUrl!, height: 250, fit: BoxFit.cover),
                const SizedBox(height: 16),
                Text(product.name, style: Theme.of(context).textTheme.headlineSmall),
                const SizedBox(height: 8),
                Text('${product.price} ريال يمني', style: TextStyle(fontSize: 20, color: Theme.of(context).primaryColor, fontWeight: FontWeight.bold)),
                const SizedBox(height: 16),
                if (product.description != null && product.description!.isNotEmpty)
                  Text('الوصف: ${product.description}', style: Theme.of(context).textTheme.bodyLarge),
                const SizedBox(height: 8),
                if (product.category != null)
                  Text('الفئة: ${product.category}'),
                const SizedBox(height: 8),
                if (product.location != null)
                  Text('الموقع: ${product.location}'),
                const SizedBox(height: 16),
                Text('البائع: ${product.sellerName ?? 'غير معروف'}'),
                if (product.sellerPhotoUrl != null)
                  Padding(
                    padding: const EdgeInsets.symmetric(vertical: 8.0),
                    child: CircleAvatar(backgroundImage: NetworkImage(product.sellerPhotoUrl!), radius: 30),
                  ),
                const SizedBox(height: 24),
                // زر التواصل مع البائع (إذا لم يكن المنتج ملك المستخدم الحالي)
                if (product.sellerId != userId)
                  ElevatedButton.icon(
                    onPressed: () async {
                      final chatService = ChatService(userId: userId);
                      try {
                        final chatId = await chatService.getOrCreateChatWithUser(product.sellerId);
                        Navigator.of(context).push(MaterialPageRoute(
                          builder: (_) => ChatScreen(chatId: chatId, userId: userId, otherUserName: product.sellerName ?? 'بائع',
                           otherUserPhotoUrl: product.sellerPhotoUrl),
                        ));
                      } catch (e) {
                        ScaffoldMessenger.of(context).showSnackBar(SnackBar(content: Text('فشل بدء المحادثة: $e')));
                      }
                    },
                    icon: const Icon(Icons.chat_bubble_outline),
                    label: const Text('تواصل مع البائع'),
                    style: ElevatedButton.styleFrom(padding: const EdgeInsets.symmetric(vertical: 16)),
                  ),
                // أزرار التعديل والحذف (إذا كان المنتج ملك المستخدم الحالي)
                if (product.sellerId == userId) ...[
                  ElevatedButton.icon(
                    onPressed: () {
                      Navigator.of(context).push(MaterialPageRoute(
                        builder: (_) => AddEditProductScreen(userId: userId, product: product),
                      ));
                    },
                    icon: const Icon(Icons.edit),
                    label: const Text('تعديل المنتج'),
                  ),
                  const SizedBox(height: 10),
                  ElevatedButton.icon(
                    onPressed: () async {
                      final confirmDelete = await showDialog<bool>(
                        context: context,
                        builder: (context) => AlertDialog(
                          title: const Text('تأكيد الحذف'),
                          content: const Text('هل أنت متأكد أنك تريد حذف هذا المنتج؟'),
                          actions: [
                            TextButton(
                              onPressed: () => Navigator.of(context).pop(false),
                              child: const Text('إلغاء'),
                            ),
                            TextButton(
                              onPressed: () => Navigator.of(context).pop(true),
                              child: const Text('حذف'),
                            ),
                          ],
                        ),
                      );
                      if (confirmDelete == true) {
                        await marketService.deleteProduct(productId);
                        Navigator.of(context).pop();
                      }
                    },
                    icon: const Icon(Icons.delete, color: Colors.white),
                    label: const Text('حذف المنتج', style: TextStyle(color: Colors.white)),
                    style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
                  ),
                ],
              ],
            ),
          );
        },
      ),
    );
  }
}

// ======================================================
// شاشة إضافة/تعديل منتج (Add/Edit Product Screen)
// ======================================================

class AddEditProductScreen extends StatefulWidget {
  final String userId;
  final ProductModel? product;
  const AddEditProductScreen({super.key, required this.userId, this.product});

  @override
  State<AddEditProductScreen> createState() => _AddEditProductScreenState();
}

class _AddEditProductScreenState extends State<AddEditProductScreen> {
  final _formKey = GlobalKey<FormState>();
  late TextEditingController _nameController;
  late TextEditingController _priceController;
  late TextEditingController _descriptionController;
  late TextEditingController _locationController;
  String? _selectedCategory;
  File? _imageFile;
  bool _isLoading = false;
  final ImageService _imageService = ImageService();
  final List<String> _categories = ['خضروات', 'فواكه', 'حبوب', 'أسمدة', 'مبيدات', 'أدوات زراعية', 'أخرى'];

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.product?.name ?? '');
    _priceController = TextEditingController(text: widget.product?.price.toString() ?? '');
    _descriptionController = TextEditingController(text: widget.product?.description ?? '');
    _locationController = TextEditingController(text: widget.product?.location ?? '');
    _selectedCategory = widget.product?.category;
    // التأكد من أن الفئة المختارة موجودة في القائمة، وإلا يتم تعيينها إلى null
    if (_selectedCategory != null && !_categories.contains(_selectedCategory)) {
      _selectedCategory = null;
    }
  }

  Future<void> _pickImage(ImageSource source) async {
    final pickedFile = source == ImageSource.camera
        ? await _imageService.captureImageFromCamera()
        : await _imageService.pickImageFromGallery();

    if (pickedFile != null) {
      setState(() {
        _imageFile = pickedFile;
      });
    }
  }

  Future<void> _saveProduct() async {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _isLoading = true;
      });

      final marketService = MarketService(userId: widget.userId);

      try {
        if (widget.product == null) {
          // إضافة منتج جديد
          await marketService.addProduct(
            _nameController.text.trim(),
            double.parse(_priceController.text.trim()),
            _descriptionController.text.trim(),
            _selectedCategory,
            _imageFile,
            _locationController.text.trim(),
          );
        } else {
          // تعديل منتج موجود
          final updatedProduct = ProductModel(
            id: widget.product!.id,
            name: _nameController.text.trim(),
            price: double.parse(_priceController.text.trim()),
            description: _descriptionController.text.trim(),
            category: _selectedCategory,
            imageUrl: widget.product!.imageUrl, // سيتم تحديثه إذا تم اختيار صورة جديدة
            sellerId: widget.userId,
            sellerName: widget.product!.sellerName, // الحفاظ على اسم البائع الأصلي
            sellerPhotoUrl: widget.product!.sellerPhotoUrl, // الحفاظ على صورة البائع الأصلية
            location: _locationController.text.trim(),
            timestamp: widget.product!.timestamp, // الحفاظ على الطابع الزمني الأصلي
          );
          await marketService.updateProduct(updatedProduct, imageFile: _imageFile);
        }
        Navigator.of(context).pop(); // العودة إلى الشاشة السابقة
        if (widget.product == null) Navigator.of(context).pop(); // العودة مرتين في حالة الإضافة

      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('فشل حفظ المنتج: ${e.toString()}')),
        );
      } finally {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: Text(widget.product == null ? 'إضافة منتج جديد' : 'تعديل المنتج')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: ListView(
            children: [
              TextFormField(
                controller: _nameController,
                decoration: const InputDecoration(labelText: 'اسم المنتج'),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'الرجاء إدخال اسم المنتج';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _priceController,
                decoration: const InputDecoration(labelText: 'السعر (ريال يمني)'),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'الرجاء إدخال السعر';
                  }
                  final price = double.tryParse(value);
                  if (price == null || price <= 0) {
                    return 'الرجاء إدخال سعر صالح';
                  }
                  return null;
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _descriptionController,
                decoration: const InputDecoration(labelText: 'الوصف (اختياري)'),
                maxLines: 3,
              ),
              const SizedBox(height: 16),
              DropdownButtonFormField<String>(
                value: _selectedCategory,
                decoration: const InputDecoration(labelText: 'الفئة (اختياري)'),
                items: _categories.map((String value) {
                  return DropdownMenuItem<String>(
                    value: value,
                    child: Text(value),
                  );
                }).toList(),
                onChanged: (String? newValue) {
                  setState(() {
                    _selectedCategory = newValue;
                  });
                },
              ),
              const SizedBox(height: 16),
              TextFormField(
                controller: _locationController,
                decoration: const InputDecoration(labelText: 'الموقع (اختياري)'),
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  const Text('صورة المنتج (اختياري):'),
                  const SizedBox(width: 10),
                  IconButton(icon: const Icon(Icons.camera_alt), onPressed: () => _pickImage(ImageSource.camera)),
                  IconButton(icon: const Icon(Icons.photo_library), onPressed: () => _pickImage(ImageSource.gallery)),
                ],
              ),
              if (_imageFile != null)
                Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Image.file(_imageFile!, height: 150, fit: BoxFit.cover),
                )
              else if (widget.product?.imageUrl != null)
                 Padding(
                  padding: const EdgeInsets.symmetric(vertical: 8.0),
                  child: Image.network(widget.product!.imageUrl!, height: 150, fit: BoxFit.cover),
                ),
              const SizedBox(height: 24),
              _isLoading
                  ? const Center(child: CircularProgressIndicator())
                  : ElevatedButton(
                      onPressed: _saveProduct,
                      child: const Text('حفظ المنتج'),
                      style: ElevatedButton.styleFrom(minimumSize: const Size(double.infinity, 50)),
                    ),
            ],
          ),
        ),
      ),
    );
  }
}

// ======================================================
// شاشة قائمة المحادثات (Chat List Screen)
// ======================================================

class ChatListScreen extends StatelessWidget {
  final String userId;
  const ChatListScreen({super.key, required this.userId});

  @override
  Widget build(BuildContext context) {
    final chatService = ChatService(userId: userId);
    return Scaffold(
      body: StreamBuilder<List<ChatModel>>(
        stream: chatService.getChats(),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }
          if (snapshot.hasError) {
            return Center(child: Text('خطأ: ${snapshot.error}'));
          }
          if (!snapshot.hasData || snapshot.data!.isEmpty) {
            return const Center(child: Text('لا توجد محادثات حاليًا.'));
          }

          final chats = snapshot.data!;
          return ListView.builder(
            itemCount: chats.length,
            itemBuilder: (context, index) {
              final chat = chats[index];
              // تحديد معلومات الطرف الآخر في المحادثة
              final otherParticipantId = chat.participants.firstWhere((id) => id != userId, orElse: () => '');
              final otherParticipantInfo = chat.participantInfo[otherParticipantId];
              final otherUserName = otherParticipantInfo?["name"] ?? "مستخدم غير معروف";
              final otherUserPhotoUrl = otherParticipantInfo?["photoUrl"];

              return Card(
                margin: const EdgeInsets.symmetric(horizontal: 8.0, vertical: 4.0),
                child: ListTile(
                  leading: CircleAvatar(
                    backgroundImage: otherUserPhotoUrl != null ? NetworkImage(otherUserPhotoUrl) : null,
                    child: otherUserPhotoUrl == null ? const Icon(Icons.person) : null,
                  ),
                  title: Text(otherUserName, style: const TextStyle(fontWeight: FontWeight.bold)),
                  subtitle: Text(chat.lastMessage ?? 'لا توجد رسائل', maxLines: 1, overflow: TextOverflow.ellipsis),
                  trailing: chat.lastMessageTimestamp != null
                      ? Text(
                          // تنسيق الوقت بشكل أفضل
                          TimeOfDay.fromDateTime(chat.lastMessageTimestamp!.toDate()).format(context),
                          style: Theme.of(context).textTheme.bodySmall,
                        )
                      : null,
                  onTap: () {
                    Navigator.of(context).push(MaterialPageRoute(
                      builder: (_) => ChatScreen(
                        chatId: chat.id,
                        userId: userId,
                        otherUserName: otherUserName,
                        otherUserPhotoUrl: otherUserPhotoUrl,
                      ),
                    ));
                  },
                ),
              );
            },
          );
        },
      ),
    );
  }
}

// ======================================================
// شاشة المحادثة (Chat Screen)
// ======================================================

class ChatScreen extends StatefulWidget {
  final String chatId;
  final String userId;
  final String otherUserName;
  final String? otherUserPhotoUrl;

  const ChatScreen({
    super.key,
    required this.chatId,
    required this.userId,
    required this.otherUserName,
    this.otherUserPhotoUrl,
  });

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  void _sendMessage() async {
    if (_messageController.text.trim().isEmpty) return;

    final chatService = ChatService(userId: widget.userId);
    try {
      await chatService.sendMessage(widget.chatId, _messageController.text.trim());
      _messageController.clear();
      // التمرير إلى أسفل القائمة بعد إرسال الرسالة
      _scrollController.animateTo(
        0.0,
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeOut,
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('فشل إرسال الرسالة: ${e.toString()}')),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final chatService = ChatService(userId: widget.userId);
    return Scaffold(
      appBar: AppBar(
        title: Row(
          children: [
            if (widget.otherUserPhotoUrl != null)
              CircleAvatar(
                backgroundImage: NetworkImage(widget.otherUserPhotoUrl!),
                radius: 18,
              )
            else
              const CircleAvatar(child: Icon(Icons.person), radius: 18),
            const SizedBox(width: 10),
            Text(widget.otherUserName),
          ],
        ),
      ),
      body: Column(
        children: [
          Expanded(
            child: StreamBuilder<List<MessageModel>>(
              stream: chatService.getMessages(widget.chatId),
              builder: (context, snapshot) {
                if (snapshot.connectionState == ConnectionState.waiting) {
                  return const Center(child: CircularProgressIndicator());
                }
                if (snapshot.hasError) {
                  return Center(child: Text('خطأ: ${snapshot.error}'));
                }
                if (!snapshot.hasData || snapshot.data!.isEmpty) {
                  return const Center(child: Text('ابدأ المحادثة!'));
                }

                final messages = snapshot.data!;
                return ListView.builder(
                  controller: _scrollController,
                  reverse: true, // لعرض الرسائل من الأسفل إلى الأعلى
                  itemCount: messages.length,
                  itemBuilder: (context, index) {
                    final message = messages[index];
                    final isMe = message.senderId == widget.userId;
                    return Align(
                      alignment: isMe ? Alignment.centerRight : Alignment.centerLeft,
                      child: Container(
                        margin: const EdgeInsets.symmetric(vertical: 4.0, horizontal: 8.0),
                        padding: const EdgeInsets.all(12.0),
                        decoration: BoxDecoration(
                          color: isMe ? Theme.of(context).primaryColorLight : Colors.grey[300],
                          borderRadius: BorderRadius.circular(12.0),
                        ),
                        child: Column(
                          crossAxisAlignment: isMe ? CrossAxisAlignment.end : CrossAxisAlignment.start,
                          children: [
                            if (!isMe) // عرض اسم المرسل إذا لم تكن الرسالة مني
                              Text(
                                message.senderName,
                                style: TextStyle(fontWeight: FontWeight.bold, fontSize: 12, color: Colors.black54),
                              ),
                            Text(message.text, style: TextStyle(color: isMe ? Colors.black87 : Colors.black87)),
                            const SizedBox(height: 4),
                            Text(
                              TimeOfDay.fromDateTime(message.timestamp.toDate()).format(context),
                              style: TextStyle(fontSize: 10, color: isMe ? Colors.black54 : Colors.black54),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              children: [
                Expanded(
                  child: TextField(
                    controller: _messageController,
                    decoration: InputDecoration(
                      hintText: 'اكتب رسالتك...',
                      border: OutlineInputBorder(borderRadius: BorderRadius.circular(25.0)),
                    ),
                    textInputAction: TextInputAction.send,
                    onSubmitted: (_) => _sendMessage(),
                  ),
                ),
                const SizedBox(width: 8),
                IconButton(
                  icon: const Icon(Icons.send),
                  onPressed: _sendMessage,
                  color: Theme.of(context).primaryColor,
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

// ======================================================
// شاشة الملف الشخصي (Profile Screen)
// ======================================================

class ProfileScreen extends StatefulWidget {
  final UserModel user;
  const ProfileScreen({super.key, required this.user});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  late TextEditingController _nameController;
  late TextEditingController _emailController;
  late TextEditingController _phoneController;
  String? _userType;
  File? _profileImageFile;
  bool _isLoading = false;
  final ImageService _imageService = ImageService();

  @override
  void initState() {
    super.initState();
    _nameController = TextEditingController(text: widget.user.name);
    _emailController = TextEditingController(text: widget.user.email);
    _phoneController = TextEditingController(text: widget.user.phoneNumber ?? '');
    _userType = widget.user.userType ?? 'مزارع';
  }

  Future<void> _pickImage(ImageSource source) async {
    final pickedFile = source == ImageSource.camera
        ? await _imageService.captureImageFromCamera()
        : await _imageService.pickImageFromGallery();

    if (pickedFile != null) {
      setState(() {
        _profileImageFile = pickedFile;
      });
    }
  }

  Future<void> _updateProfile() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final authService = Provider.of<AuthService>(context, listen: false);
      final updatedUser = widget.user.copyWith(
        name: _nameController.text.trim(),
        phoneNumber: _phoneController.text.trim(),
        userType: _userType,
        // سيتم تحديث photoUrl داخل updateUserProfile إذا تم اختيار صورة جديدة
      );
      
      final resultUser = await authService.updateUserProfile(updatedUser, profileImage: _profileImageFile);
      
      // تحديث المستخدم في AuthBloc (إذا كنت تستخدم Bloc)
      // context.read<AuthBloc>().add(AuthUserUpdated(resultUser));

      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('تم تحديث الملف الشخصي بنجاح')),
      );
      // تحديث واجهة المستخدم بالبيانات الجديدة
      // يمكنك إعادة تحميل الشاشة أو تحديث widget.user
      Navigator.of(context).pop(); // العودة إلى الشاشة السابقة
      Navigator.of(context).pushReplacement(MaterialPageRoute(builder: (_) => HomeScreen(user: resultUser)));

    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('فشل تحديث الملف الشخصي: ${e.toString()}')),
      );
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('الملف الشخصي')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: ListView(
          children: [
            Center(
              child: Stack(
                children: [
                  CircleAvatar(
                    radius: 60,
                    backgroundImage: _profileImageFile != null
                        ? FileImage(_profileImageFile!)
                        : (widget.user.photoUrl != null && widget.user.photoUrl!.isNotEmpty
                            ? NetworkImage(widget.user.photoUrl!)
                            : const AssetImage('assets/images/default_avatar.png')) as ImageProvider,
                    child: (_profileImageFile == null && (widget.user.photoUrl == null || widget.user.photoUrl!.isEmpty))
                        ? const Icon(Icons.person, size: 60)
                        : null,
                  ),
                  Positioned(
                    bottom: 0,
                    right: 0,
                    child: IconButton(
                      icon: const Icon(Icons.camera_alt, color: Colors.blueAccent),
                      onPressed: () {
                        showModalBottomSheet(
                          context: context,
                          builder: (builder) => BottomSheet(
                            onClosing: () {},
                            builder: (builder) => Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                ListTile(
                                  leading: const Icon(Icons.camera_alt),
                                  title: const Text('الكاميرا'),
                                  onTap: () {
                                    _pickImage(ImageSource.camera);
                                    Navigator.of(context).pop();
                                  },
                                ),
                                ListTile(
                                  leading: const Icon(Icons.photo_library),
                                  title: const Text('المعرض'),
                                  onTap: () {
                                    _pickImage(ImageSource.gallery);
                                    Navigator.of(context).pop();
                                  },
                                ),
                              ],
                            ),
                          ),
                        );
                      },
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 24),
            TextFormField(
              controller: _nameController,
              decoration: const InputDecoration(labelText: 'الاسم الكامل'),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _emailController,
              decoration: const InputDecoration(labelText: 'البريد الإلكتروني (لا يمكن تعديله)'),
              readOnly: true,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _phoneController,
              decoration: const InputDecoration(labelText: 'رقم الهاتف'),
              keyboardType: TextInputType.phone,
            ),
            const SizedBox(height: 16),
            DropdownButtonFormField<String>(
              value: _userType,
              decoration: const InputDecoration(labelText: 'نوع المستخدم'),
              items: ['مزارع', 'مرشد زراعي', 'تاجر'].map((String value) {
                return DropdownMenuItem<String>(
                  value: value,
                  child: Text(value),
                );
              }).toList(),
              onChanged: (String? newValue) {
                setState(() {
                  _userType = newValue!;
                });
              },
            ),
            const SizedBox(height: 24),
            _isLoading
                ? const Center(child: CircularProgressIndicator())
                : ElevatedButton(
                    onPressed: _updateProfile,
                    child: const Text('حفظ التعديلات'),
                    style: ElevatedButton.styleFrom(minimumSize: const Size(double.infinity, 50)),
                  ),
            const SizedBox(height: 10),
             ElevatedButton.icon(
              onPressed: () async {
                final authService = Provider.of<AuthService>(context, listen: false);
                await authService.signOut();
                Navigator.of(context).pushAndRemoveUntil(
                  MaterialPageRoute(builder: (_) => const LoginScreen()),
                  (route) => false,
                );
              },
              icon: const Icon(Icons.logout, color: Colors.white),
              label: const Text('تسجيل الخروج', style: TextStyle(color: Colors.white)),
              style: ElevatedButton.styleFrom(backgroundColor: Colors.red, minimumSize: const Size(double.infinity, 50)),
            ),
          ],
        ),
      ),
    );
  }
}

// ======================================================
// شاشة الإعدادات (Settings Screen)
// ======================================================

class SettingsScreen extends StatefulWidget {
  const SettingsScreen({super.key});

  @override
  State<SettingsScreen> createState() => _SettingsScreenState();
}

class _SettingsScreenState extends State<SettingsScreen> {
  final SettingsService _settingsService = SettingsService();
  ThemeMode _themeMode = ThemeMode.system;
  bool _notificationsEnabled = true;
  String _selectedLanguage = 'ar'; // اللغة الافتراضية هي العربية

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    _themeMode = await _settingsService.getThemeMode();
    _notificationsEnabled = await _settingsService.getNotificationsEnabled();
    _selectedLanguage = await _settingsService.getLanguage();
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final themeProvider = Provider.of<ThemeProvider>(context, listen: false);

    return Scaffold(
      appBar: AppBar(title: const Text('الإعدادات')),
      body: ListView(
        padding: const EdgeInsets.all(16.0),
        children: [
          ListTile(
            title: const Text('الوضع الداكن'),
            trailing: DropdownButton<ThemeMode>(
              value: _themeMode,
              items: const [
                DropdownMenuItem(value: ThemeMode.system, child: Text('النظام')),
                DropdownMenuItem(value: ThemeMode.light, child: Text('فاتح')),
                DropdownMenuItem(value: ThemeMode.dark, child: Text('داكن')),
              ],
              onChanged: (ThemeMode? newMode) {
                if (newMode != null) {
                  setState(() {
                    _themeMode = newMode;
                  });
                  _settingsService.setThemeMode(newMode);
                  themeProvider.setThemeMode(newMode);
                }
              },
            ),
          ),
          SwitchListTile(
            title: const Text('تفعيل الإشعارات'),
            value: _notificationsEnabled,
            onChanged: (bool value) {
              setState(() {
                _notificationsEnabled = value;
              });
              _settingsService.setNotificationsEnabled(value);
              // هنا يمكن إضافة منطق لتفعيل/تعطيل الإشعارات فعليًا
            },
          ),
          ListTile(
            title: const Text('اللغة'),
            trailing: DropdownButton<String>(
              value: _selectedLanguage,
              items: const [
                DropdownMenuItem(value: 'ar', child: Text('العربية')),
                // يمكن إضافة لغات أخرى هنا إذا لزم الأمر
                // DropdownMenuItem(value: 'en', child: Text('English')),
              ],
              onChanged: (String? newLanguage) {
                if (newLanguage != null) {
                  setState(() {
                    _selectedLanguage = newLanguage;
                  });
                  _settingsService.setLanguage(newLanguage);
                  // هنا يمكن إضافة منطق لتغيير لغة التطبيق فعليًا
                  // على سبيل المثال، إعادة تحميل التطبيق أو استخدام مكتبة ترجمة
                  // MyApp.setLocale(context, Locale(newLanguage)); // مثال
                }
              },
            ),
          ),
          const Divider(),
          ListTile(
            title: const Text('حول التطبيق'),
            onTap: () {
              showAboutDialog(
                context: context,
                applicationName: 'SAM - مساعدك ومرشدك الزراعي',
                applicationVersion: '1.0.0',
                applicationLegalese: '© 2024 فريق SAM',
                children: [
                  const SizedBox(height: 10),
                  const Text('تطبيق SAM هو مساعدك ومرشدك الزراعي الذكي في اليمن، يهدف إلى تقديم حلول تقنية للمزارعين لتحسين إنتاجيتهم وإدارة مزارعهم بكفاءة.'),
                ],
              );
            },
          ),
        ],
      ),
    );
  }
}

// ======================================================
// مزود السمة (Theme Provider)
// ======================================================

class ThemeProvider extends ChangeNotifier {
  ThemeMode _themeMode = ThemeMode.system;
  final SettingsService _settingsService = SettingsService();

  ThemeMode get themeMode => _themeMode;

  ThemeProvider() {
    _loadThemeMode();
  }

  Future<void> _loadThemeMode() async {
    _themeMode = await _settingsService.getThemeMode();
    notifyListeners();
  }

  void setThemeMode(ThemeMode mode) {
    _themeMode = mode;
    _settingsService.setThemeMode(mode);
    notifyListeners();
  }
}

