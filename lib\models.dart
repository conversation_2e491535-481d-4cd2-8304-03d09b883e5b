// ملف النماذج (Models) - يحتوي على جميع نماذج البيانات المستخدمة في التطبيق

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import 'dart:io';

// ======================================================
// نموذج المستخدم (User Model)
// ======================================================

class UserModel extends Equatable {
  final String id;
  final String email;
  final String name;
  final String? phoneNumber;
  final String? userType;
  final String? photoUrl;

  const UserModel({
    required this.id,
    required this.email,
    required this.name,
    this.phoneNumber,
    this.userType,
    this.photoUrl,
  });

  factory UserModel.fromJson(Map<String, dynamic> json) {
    return UserModel(
      id: json['id'] as String,
      email: json['email'] as String,
      name: json['name'] as String,
      phoneNumber: json['phoneNumber'] as String?,
      userType: json['userType'] as String?,
      photoUrl: json['photoUrl'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'phoneNumber': phoneNumber,
      'userType': userType,
      'photoUrl': photoUrl,
    };
  }

  UserModel copyWith({
    String? id,
    String? email,
    String? name,
    String? phoneNumber,
    String? userType,
    String? photoUrl,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      userType: userType ?? this.userType,
      photoUrl: photoUrl ?? this.photoUrl,
    );
  }

  @override
  List<Object?> get props => [id, email, name, phoneNumber, userType, photoUrl];
}

// ======================================================
// نموذج بيانات الحساسات (Sensor Data Model)
// ======================================================

class SensorDataModel extends Equatable {
  final double temperature;
  final int soilMoisture;
  final int waterLevel;
  final Timestamp timestamp;

  const SensorDataModel({
    required this.temperature,
    required this.soilMoisture,
    required this.waterLevel,
    required this.timestamp,
  });

  factory SensorDataModel.fromJson(Map<String, dynamic> json) {
    return SensorDataModel(
      temperature: (json['temperature'] as num).toDouble(),
      soilMoisture: (json['soilMoisture'] as num).toInt(),
      waterLevel: (json['waterLevel'] as num).toInt(),
      timestamp: json['timestamp'] as Timestamp,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'temperature': temperature,
      'soilMoisture': soilMoisture,
      'waterLevel': waterLevel,
      'timestamp': timestamp,
    };
  }

  @override
  List<Object?> get props => [temperature, soilMoisture, waterLevel, timestamp];
}

// ======================================================
// نموذج نظام الري (Irrigation System Model)
// ======================================================

class IrrigationSystemModel extends Equatable {
  final String id;
  final String userId;
  final String name;
  final String espId;
  final bool isAutomatic;
  final int moistureThreshold;
  final int wateringDurationMinutes;
  final bool isPumpOn;
  final SensorDataModel? sensorData;

  const IrrigationSystemModel({
    required this.id,
    required this.userId,
    required this.name,
    required this.espId,
    required this.isAutomatic,
    required this.moistureThreshold,
    required this.wateringDurationMinutes,
    required this.isPumpOn,
    this.sensorData,
  });

  factory IrrigationSystemModel.fromJson(Map<String, dynamic> json) {
    return IrrigationSystemModel(
      id: json['id'] as String,
      userId: json['userId'] as String,
      name: json['name'] as String,
      espId: json['espId'] as String,
      isAutomatic: json['isAutomatic'] as bool,
      moistureThreshold: (json['moistureThreshold'] as num).toInt(),
      wateringDurationMinutes: (json['wateringDurationMinutes'] as num).toInt(),
      isPumpOn: json['isPumpOn'] as bool,
      sensorData: json['sensorData'] != null
          ? SensorDataModel.fromJson(json['sensorData'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'name': name,
      'espId': espId,
      'isAutomatic': isAutomatic,
      'moistureThreshold': moistureThreshold,
      'wateringDurationMinutes': wateringDurationMinutes,
      'isPumpOn': isPumpOn,
      'sensorData': sensorData?.toJson(),
    };
  }

  IrrigationSystemModel copyWith({
    String? id,
    String? userId,
    String? name,
    String? espId,
    bool? isAutomatic,
    int? moistureThreshold,
    int? wateringDurationMinutes,
    bool? isPumpOn,
    SensorDataModel? sensorData,
  }) {
    return IrrigationSystemModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      name: name ?? this.name,
      espId: espId ?? this.espId,
      isAutomatic: isAutomatic ?? this.isAutomatic,
      moistureThreshold: moistureThreshold ?? this.moistureThreshold,
      wateringDurationMinutes: wateringDurationMinutes ?? this.wateringDurationMinutes,
      isPumpOn: isPumpOn ?? this.isPumpOn,
      sensorData: sensorData ?? this.sensorData,
    );
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        name,
        espId,
        isAutomatic,
        moistureThreshold,
        wateringDurationMinutes,
        isPumpOn,
        sensorData,
      ];
}

// ======================================================
// نموذج تشخيص أمراض النباتات (Disease Diagnosis Model)
// ======================================================

class DiseaseDiagnosisModel extends Equatable {
  final String id;
  final String userId;
  final String diseaseName;
  final double confidence;
  final String recommendations;
  final String? imageUrl;
  final Timestamp timestamp;

  const DiseaseDiagnosisModel({
    required this.id,
    required this.userId,
    required this.diseaseName,
    required this.confidence,
    required this.recommendations,
    this.imageUrl,
    required this.timestamp,
  });

  factory DiseaseDiagnosisModel.fromJson(Map<String, dynamic> json) {
    return DiseaseDiagnosisModel(
      id: json['id'] as String,
      userId: json['userId'] as String,
      diseaseName: json['diseaseName'] as String,
      confidence: (json['confidence'] as num).toDouble(),
      recommendations: json['recommendations'] as String,
      imageUrl: json['imageUrl'] as String?,
      timestamp: json['timestamp'] as Timestamp,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'userId': userId,
      'diseaseName': diseaseName,
      'confidence': confidence,
      'recommendations': recommendations,
      'imageUrl': imageUrl,
      'timestamp': timestamp,
    };
  }

  @override
  List<Object?> get props => [id, userId, diseaseName, confidence, recommendations, imageUrl, timestamp];
}

// ======================================================
// نموذج المنتج (Product Model)
// ======================================================

class ProductModel extends Equatable {
  final String id;
  final String name;
  final double price;
  final String? description;
  final String? category;
  final String? imageUrl;
  final String sellerId;
  final String? sellerName;
  final String? sellerPhotoUrl;
  final String? location;
  final Timestamp timestamp;

  const ProductModel({
    required this.id,
    required this.name,
    required this.price,
    this.description,
    this.category,
    this.imageUrl,
    required this.sellerId,
    this.sellerName,
    this.sellerPhotoUrl,
    this.location,
    required this.timestamp,
  });

  factory ProductModel.fromJson(Map<String, dynamic> json) {
    return ProductModel(
      id: json['id'] as String,
      name: json['name'] as String,
      price: (json['price'] as num).toDouble(),
      description: json['description'] as String?,
      category: json['category'] as String?,
      imageUrl: json['imageUrl'] as String?,
      sellerId: json['sellerId'] as String,
      sellerName: json['sellerName'] as String?,
      sellerPhotoUrl: json['sellerPhotoUrl'] as String?,
      location: json['location'] as String?,
      timestamp: json['timestamp'] as Timestamp,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'price': price,
      'description': description,
      'category': category,
      'imageUrl': imageUrl,
      'sellerId': sellerId,
      'sellerName': sellerName,
      'sellerPhotoUrl': sellerPhotoUrl,
      'location': location,
      'timestamp': timestamp,
    };
  }

  @override
  List<Object?> get props => [
        id,
        name,
        price,
        description,
        category,
        imageUrl,
        sellerId,
        sellerName,
        sellerPhotoUrl,
        location,
        timestamp,
      ];
}

// ======================================================
// نموذج المحادثة (Chat Model)
// ======================================================

class ChatModel extends Equatable {
  final String id;
  final List<String> participants;
  final Map<String, Map<String, dynamic>> participantInfo;
  final String? lastMessage;
  final Timestamp? lastMessageTimestamp;

  const ChatModel({
    required this.id,
    required this.participants,
    required this.participantInfo,
    this.lastMessage,
    this.lastMessageTimestamp,
  });

  factory ChatModel.fromJson(Map<String, dynamic> json) {
    return ChatModel(
      id: json['id'] as String,
      participants: List<String>.from(json['participants'] as List),
      participantInfo: Map<String, Map<String, dynamic>>.from(
        (json['participantInfo'] as Map).map(
          (key, value) => MapEntry(key as String, Map<String, dynamic>.from(value as Map)),
        ),
      ),
      lastMessage: json['lastMessage'] as String?,
      lastMessageTimestamp: json['lastMessageTimestamp'] as Timestamp?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'participants': participants,
      'participantInfo': participantInfo,
      'lastMessage': lastMessage,
      'lastMessageTimestamp': lastMessageTimestamp,
    };
  }

  @override
  List<Object?> get props => [id, participants, participantInfo, lastMessage, lastMessageTimestamp];
}

// ======================================================
// نموذج الرسالة (Message Model)
// ======================================================

class MessageModel extends Equatable {
  final String id;
  final String chatId;
  final String senderId;
  final String senderName;
  final String text;
  final Timestamp timestamp;

  const MessageModel({
    required this.id,
    required this.chatId,
    required this.senderId,
    required this.senderName,
    required this.text,
    required this.timestamp,
  });

  factory MessageModel.fromJson(Map<String, dynamic> json) {
    return MessageModel(
      id: json['id'] as String,
      chatId: json['chatId'] as String,
      senderId: json['senderId'] as String,
      senderName: json['senderName'] as String,
      text: json['text'] as String,
      timestamp: json['timestamp'] as Timestamp,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'chatId': chatId,
      'senderId': senderId,
      'senderName': senderName,
      'text': text,
      'timestamp': timestamp,
    };
  }

  @override
  List<Object?> get props => [id, chatId, senderId, senderName, text, timestamp];
}

// ======================================================
// نماذج حالة المصادقة (Auth State Models)
// ======================================================

enum AuthStatus { initial, loading, authenticated, unauthenticated, error }

class AuthState extends Equatable {
  final AuthStatus status;
  final UserModel? user;
  final String? errorMessage;

  const AuthState({
    this.status = AuthStatus.initial,
    this.user,
    this.errorMessage,
  });

  AuthState copyWith({
    AuthStatus? status,
    UserModel? user,
    String? errorMessage,
  }) {
    return AuthState(
      status: status ?? this.status,
      user: user ?? this.user,
      errorMessage: errorMessage ?? this.errorMessage,
    );
  }

  @override
  List<Object?> get props => [status, user, errorMessage];
}

// ======================================================
// نماذج أحداث المصادقة (Auth Event Models)
// ======================================================

abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

class AuthCheckStatus extends AuthEvent {}

class AuthLoginRequested extends AuthEvent {
  final String email;
  final String password;

  const AuthLoginRequested({required this.email, required this.password});

  @override
  List<Object?> get props => [email, password];
}

class AuthRegisterRequested extends AuthEvent {
  final String email;
  final String password;
  final String name;
  final String? phoneNumber;
  final String? userType;

  const AuthRegisterRequested({
    required this.email,
    required this.password,
    required this.name,
    this.phoneNumber,
    this.userType,
  });

  @override
  List<Object?> get props => [email, password, name, phoneNumber, userType];
}

class AuthLogoutRequested extends AuthEvent {}

class AuthForgotPasswordRequested extends AuthEvent {
  final String email;

  const AuthForgotPasswordRequested({required this.email});

  @override
  List<Object?> get props => [email];
}

class AuthUserUpdated extends AuthEvent {
  final UserModel user;

  const AuthUserUpdated(this.user);

  @override
  List<Object?> get props => [user];
}

// ======================================================
// نماذج حالة نظام الري (Irrigation State Models)
// ======================================================

abstract class IrrigationState extends Equatable {
  const IrrigationState();

  @override
  List<Object?> get props => [];
}

class IrrigationInitial extends IrrigationState {}

class IrrigationLoading extends IrrigationState {}

class IrrigationSystemsLoaded extends IrrigationState {
  final List<IrrigationSystemModel> systems;

  const IrrigationSystemsLoaded(this.systems);

  @override
  List<Object?> get props => [systems];
}

class IrrigationSystemLoaded extends IrrigationState {
  final IrrigationSystemModel system;

  const IrrigationSystemLoaded(this.system);

  @override
  List<Object?> get props => [system];
}

class IrrigationSystemAdded extends IrrigationState {
  final IrrigationSystemModel system;

  const IrrigationSystemAdded(this.system);

  @override
  List<Object?> get props => [system];
}

class IrrigationSystemUpdated extends IrrigationState {
  final IrrigationSystemModel system;

  const IrrigationSystemUpdated(this.system);

  @override
  List<Object?> get props => [system];
}

class IrrigationSystemDeleted extends IrrigationState {
  final String systemId;

  const IrrigationSystemDeleted(this.systemId);

  @override
  List<Object?> get props => [systemId];
}

class IrrigationError extends IrrigationState {
  final String message;

  const IrrigationError(this.message);

  @override
  List<Object?> get props => [message];
}

// ======================================================
// نماذج أحداث نظام الري (Irrigation Event Models)
// ======================================================

abstract class IrrigationEvent extends Equatable {
  const IrrigationEvent();

  @override
  List<Object?> get props => [];
}

class LoadIrrigationSystems extends IrrigationEvent {}

class LoadIrrigationSystem extends IrrigationEvent {
  final String systemId;

  const LoadIrrigationSystem(this.systemId);

  @override
  List<Object?> get props => [systemId];
}

class AddIrrigationSystem extends IrrigationEvent {
  final IrrigationSystemModel system;

  const AddIrrigationSystem(this.system);

  @override
  List<Object?> get props => [system];
}

class UpdateIrrigationSystem extends IrrigationEvent {
  final IrrigationSystemModel system;

  const UpdateIrrigationSystem(this.system);

  @override
  List<Object?> get props => [system];
}

class DeleteIrrigationSystem extends IrrigationEvent {
  final String systemId;

  const DeleteIrrigationSystem(this.systemId);

  @override
  List<Object?> get props => [systemId];
}

class TogglePump extends IrrigationEvent {
  final String systemId;
  final bool isPumpOn;

  const TogglePump(this.systemId, this.isPumpOn);

  @override
  List<Object?> get props => [systemId, isPumpOn];
}

class ToggleAutomaticMode extends IrrigationEvent {
  final String systemId;
  final bool isAutomatic;

  const ToggleAutomaticMode(this.systemId, this.isAutomatic);

  @override
  List<Object?> get props => [systemId, isAutomatic];
}

// ======================================================
// نماذج حالة تشخيص الأمراض (Disease Diagnosis State Models)
// ======================================================

abstract class DiseaseDiagnosisState extends Equatable {
  const DiseaseDiagnosisState();

  @override
  List<Object?> get props => [];
}

class DiseaseDiagnosisInitial extends DiseaseDiagnosisState {}

class DiseaseDiagnosisLoading extends DiseaseDiagnosisState {}

class DiseaseDiagnosisSuccess extends DiseaseDiagnosisState {
  final DiseaseDiagnosisModel diagnosis;

  const DiseaseDiagnosisSuccess(this.diagnosis);

  @override
  List<Object?> get props => [diagnosis];
}

class DiseaseDiagnosisError extends DiseaseDiagnosisState {
  final String message;

  const DiseaseDiagnosisError(this.message);

  @override
  List<Object?> get props => [message];
}

// ======================================================
// نماذج أحداث تشخيص الأمراض (Disease Diagnosis Event Models)
// ======================================================

abstract class DiseaseDiagnosisEvent extends Equatable {
  const DiseaseDiagnosisEvent();

  @override
  List<Object?> get props => [];
}

class DiagnosePlantDisease extends DiseaseDiagnosisEvent {
  final File imageFile;

  const DiagnosePlantDisease(this.imageFile);

  @override
  List<Object?> get props => [imageFile];
}

// ======================================================
// نماذج حالة السوق (Market State Models)
// ======================================================

abstract class MarketState extends Equatable {
  const MarketState();

  @override
  List<Object?> get props => [];
}

class MarketInitial extends MarketState {}

class MarketLoading extends MarketState {}

class ProductsLoaded extends MarketState {
  final List<ProductModel> products;

  const ProductsLoaded(this.products);

  @override
  List<Object?> get props => [products];
}

class ProductLoaded extends MarketState {
  final ProductModel product;

  const ProductLoaded(this.product);

  @override
  List<Object?> get props => [product];
}

class ProductAdded extends MarketState {
  final ProductModel product;

  const ProductAdded(this.product);

  @override
  List<Object?> get props => [product];
}

class ProductUpdated extends MarketState {
  final ProductModel product;

  const ProductUpdated(this.product);

  @override
  List<Object?> get props => [product];
}

class ProductDeleted extends MarketState {
  final String productId;

  const ProductDeleted(this.productId);

  @override
  List<Object?> get props => [productId];
}

class MarketError extends MarketState {
  final String message;

  const MarketError(this.message);

  @override
  List<Object?> get props => [message];
}

// ======================================================
// نماذج أحداث السوق (Market Event Models)
// ======================================================

abstract class MarketEvent extends Equatable {
  const MarketEvent();

  @override
  List<Object?> get props => [];
}

class LoadProducts extends MarketEvent {
  final String? category;
  final String? searchQuery;

  const LoadProducts({this.category, this.searchQuery});

  @override
  List<Object?> get props => [category, searchQuery];
}

class LoadProduct extends MarketEvent {
  final String productId;

  const LoadProduct(this.productId);

  @override
  List<Object?> get props => [productId];
}

class AddProduct extends MarketEvent {
  final ProductModel product;

  const AddProduct(this.product);

  @override
  List<Object?> get props => [product];
}

class UpdateProduct extends MarketEvent {
  final ProductModel product;

  const UpdateProduct(this.product);

  @override
  List<Object?> get props => [product];
}

class DeleteProduct extends MarketEvent {
  final String productId;

  const DeleteProduct(this.productId);

  @override
  List<Object?> get props => [productId];
}

// ======================================================
// نماذج حالة المحادثات (Chat State Models)
// ======================================================

abstract class ChatState extends Equatable {
  const ChatState();

  @override
  List<Object?> get props => [];
}

class ChatInitial extends ChatState {}

class ChatLoading extends ChatState {}

class ChatsLoaded extends ChatState {
  final List<ChatModel> chats;

  const ChatsLoaded(this.chats);

  @override
  List<Object?> get props => [chats];
}

class ChatLoaded extends ChatState {
  final String chatId;
  final List<MessageModel>? messages;

  const ChatLoaded(this.chatId, {this.messages});

  @override
  List<Object?> get props => [chatId, messages];
}

class MessagesLoaded extends ChatState {
  final List<MessageModel> messages;

  const MessagesLoaded(this.messages);

  @override
  List<Object?> get props => [messages];
}

class MessageSent extends ChatState {
  final MessageModel message;

  const MessageSent(this.message);

  @override
  List<Object?> get props => [message];
}

class ChatError extends ChatState {
  final String message;

  const ChatError(this.message);

  @override
  List<Object?> get props => [message];
}

// ======================================================
// نماذج أحداث المحادثات (Chat Event Models)
// ======================================================

abstract class ChatEvent extends Equatable {
  const ChatEvent();

  @override
  List<Object?> get props => [];
}

class LoadChats extends ChatEvent {}

class LoadOrCreateChatWithUser extends ChatEvent {
  final String userId;

  const LoadOrCreateChatWithUser(this.userId);

  @override
  List<Object?> get props => [userId];
}

class LoadMessages extends ChatEvent {
  final String chatId;

  const LoadMessages(this.chatId);

  @override
  List<Object?> get props => [chatId];
}

class SendMessage extends ChatEvent {
  final MessageModel message;

  const SendMessage(this.message);

  @override
  List<Object?> get props => [message];
}
