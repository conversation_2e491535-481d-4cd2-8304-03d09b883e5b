// ملف main.dart الرئيسي للتطبيق

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';

import 'models.dart';
import 'services.dart';
import 'screens.dart';

// ======================================================
// نقطة بداية التطبيق
// ======================================================

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp();
  
  // تعيين اتجاه التطبيق من اليمين إلى اليسار للغة العربية
  SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);
  
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // توفير خدمات التطبيق
        Provider<AuthService>(create: (_) => AuthService()),
        Provider<UserRepository>(create: (_) => UserRepository()),
        Provider<IrrigationSystemRepository>(create: (_) => IrrigationSystemRepository()),
        Provider<DiseaseDiagnosisRepository>(create: (_) => DiseaseDiagnosisRepository()),
        Provider<MarketRepository>(create: (_) => MarketRepository()),
        Provider<ChatRepository>(create: (_) => ChatRepository()),
        
        // توفير BLoC للمصادقة
        BlocProvider<AuthBloc>(
          create: (context) => AuthBloc(
            context.read<AuthService>(),
            context.read<UserRepository>(),
          )..add(AuthCheckStatus()),
        ),
      ],
      child: MaterialApp(
        title: AppStrings.appName,
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: ThemeMode.light, // يمكن تغييره لاحقاً حسب إعدادات المستخدم
        debugShowCheckedModeBanner: false,
        localizationsDelegates: const [
          GlobalMaterialLocalizations.delegate,
          GlobalWidgetsLocalizations.delegate,
          GlobalCupertinoLocalizations.delegate,
        ],
        supportedLocales: const [
          Locale('ar', ''), // العربية فقط
        ],
        locale: const Locale('ar', ''),
        initialRoute: '/',
        routes: {
          '/': (context) => const SplashScreen(),
          '/login': (context) => const LoginScreen(),
          '/register': (context) => const RegisterScreen(),
          '/forgot_password': (context) => const ForgotPasswordScreen(),
          '/home': (context) => const MainAppScreen(),
          '/profile': (context) => const ProfileScreen(),
          '/edit_profile': (context) => const EditProfileScreen(),
          '/settings': (context) => const SettingsScreen(),
          '/irrigation_list': (context) => const IrrigationSystemsListScreen(),
          '/add_edit_irrigation': (context) => const AddEditIrrigationSystemScreen(),
          '/diseases': (context) => const PlantDiseasesScreen(),
          '/market': (context) => const MarketScreen(),
          '/add_product': (context) => const AddEditProductScreen(),
          '/chat_list': (context) => const ChatListScreen(),
          '/user_search': (context) => const UserSearchScreen(),
        },
      ),
    );
  }
}

// ======================================================
// ثوابت التطبيق
// ======================================================

class AppStrings {
  // اسم التطبيق
  static const String appName = 'SAM';
  
  // عام
  static const String welcome = 'مرحباً بك في تطبيق SAM';
  static const String fieldRequired = 'هذا الحقل مطلوب';
  static const String filter = 'تصفية';
  static const String currency = 'ريال';
  static const String locationNotSpecified = 'الموقع غير محدد';
  static const String uncategorized = 'غير مصنف';
  
  // المصادقة
  static const String login = 'تسجيل الدخول';
  static const String register = 'إنشاء حساب';
  static const String forgotPassword = 'نسيت كلمة المرور؟';
  static const String createNewAccount = 'إنشاء حساب جديد';
  static const String alreadyHaveAccount = 'لديك حساب بالفعل؟ تسجيل الدخول';
  static const String email = 'البريد الإلكتروني';
  static const String password = 'كلمة المرور';
  static const String confirmPassword = 'تأكيد كلمة المرور';
  static const String name = 'الاسم';
  static const String phoneNumber = 'رقم الهاتف';
  static const String userType = 'نوع المستخدم';
  static const String invalidEmail = 'البريد الإلكتروني غير صالح';
  static const String passwordTooShort = 'كلمة المرور قصيرة جداً';
  static const String passwordsDoNotMatch = 'كلمات المرور غير متطابقة';
  static const String passwordResetEmailSent = 'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني';
  
  // أنواع المستخدمين
  static const String farmer = 'مزارع';
  static const String merchant = 'تاجر';
  static const String expert = 'خبير';
  static const String normalUser = 'مستخدم عادي';
  
  // القائمة الرئيسية
  static const String home = 'الرئيسية';
  static const String smartIrrigation = 'الري الذكي';
  static const String plantDiseases = 'أمراض النباتات';
  static const String agriculturalMarket = 'السوق الزراعي';
  static const String chat = 'المحادثات';
  
  // نظام الري
  static const String pumpStatus = 'حالة المضخة';
  static const String pumpOn = 'تعمل';
  static const String pumpOff = 'متوقفة';
  static const String turnOnPump = 'تشغيل المضخة';
  static const String turnOffPump = 'إيقاف المضخة';
  static const String automaticMode = 'الوضع التلقائي';
  static const String manualMode = 'الوضع اليدوي';
  static const String soilMoisture = 'رطوبة التربة';
  static const String temperature = 'درجة الحرارة';
  static const String waterLevel = 'مستوى الماء';
  static const String soilMoistureLevel = 'مستوى رطوبة التربة';
  static const String temperatureLevel = 'درجة الحرارة';
  static const String waterTankLevel = 'مستوى خزان الماء';
  static const String moistureThreshold = 'عتبة الرطوبة (%)';
  static const String wateringDuration = 'مدة الري (دقائق)';
  
  // أمراض النباتات
  static const String takePhoto = 'التقاط صورة';
  static const String chooseFromGallery = 'اختيار من المعرض';
  static const String diagnosePlant = 'تشخيص النبات';
  static const String diagnosisResult = 'نتيجة التشخيص';
  static const String diseaseName = 'اسم المرض';
  static const String confidenceLevel = 'مستوى الثقة';
  static const String recommendations = 'التوصيات';
  static const String noDiseaseDetected = 'لم يتم اكتشاف أي مرض';
  static const String commonPlantDiseases = 'الأمراض الشائعة';
  
  // السوق الزراعي
  static const String searchProducts = 'البحث عن منتجات';
  static const String addProduct = 'إضافة منتج';
  static const String productDetails = 'تفاصيل المنتج';
  static const String latestArticles = 'أحدث المقالات';
}

class AppColors {
  static const Color primaryColor = Color(0xFF2E7D32); // أخضر داكن
  static const Color accentColor = Color(0xFF66BB6A); // أخضر فاتح
  static const Color errorColor = Color(0xFFD32F2F); // أحمر
  static const Color successColor = Color(0xFF388E3C); // أخضر نجاح
  static const Color warningColor = Color(0xFFFFA000); // برتقالي تحذير
  
  // ألوان المؤشرات
  static const Color temperatureColor = Color(0xFFE57373); // أحمر فاتح
  static const Color soilMoistureColor = Color(0xFF64B5F6); // أزرق فاتح
  static const Color waterLevelColor = Color(0xFF4FC3F7); // أزرق سماوي
}

class AppTheme {
  static ThemeData get lightTheme {
    return ThemeData(
      primaryColor: AppColors.primaryColor,
      colorScheme: ColorScheme.fromSwatch().copyWith(
        primary: AppColors.primaryColor,
        secondary: AppColors.accentColor,
        error: AppColors.errorColor,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: AppColors.primaryColor,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontFamily: 'Tajawal',
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.primaryColor,
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.primaryColor, width: 2),
        ),
        floatingLabelStyle: const TextStyle(color: AppColors.primaryColor),
      ),
      fontFamily: 'Tajawal',
      textTheme: const TextTheme(
        headlineLarge: TextStyle(fontFamily: 'Tajawal'),
        headlineMedium: TextStyle(fontFamily: 'Tajawal'),
        headlineSmall: TextStyle(fontFamily: 'Tajawal'),
        titleLarge: TextStyle(fontFamily: 'Tajawal'),
        titleMedium: TextStyle(fontFamily: 'Tajawal'),
        titleSmall: TextStyle(fontFamily: 'Tajawal'),
        bodyLarge: TextStyle(fontFamily: 'Tajawal'),
        bodyMedium: TextStyle(fontFamily: 'Tajawal'),
        bodySmall: TextStyle(fontFamily: 'Tajawal'),
      ),
    );
  }

  static ThemeData get darkTheme {
    return ThemeData.dark().copyWith(
      primaryColor: AppColors.primaryColor,
      colorScheme: ColorScheme.fromSwatch(
        brightness: Brightness.dark,
      ).copyWith(
        primary: AppColors.primaryColor,
        secondary: AppColors.accentColor,
        error: AppColors.errorColor,
      ),
      appBarTheme: const AppBarTheme(
        backgroundColor: Colors.black,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontFamily: 'Tajawal',
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primaryColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.accentColor,
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(8),
          borderSide: const BorderSide(color: AppColors.accentColor, width: 2),
        ),
        floatingLabelStyle: const TextStyle(color: AppColors.accentColor),
      ),
      fontFamily: 'Tajawal',
      textTheme: const TextTheme(
        headlineLarge: TextStyle(fontFamily: 'Tajawal'),
        headlineMedium: TextStyle(fontFamily: 'Tajawal'),
        headlineSmall: TextStyle(fontFamily: 'Tajawal'),
        titleLarge: TextStyle(fontFamily: 'Tajawal'),
        titleMedium: TextStyle(fontFamily: 'Tajawal'),
        titleSmall: TextStyle(fontFamily: 'Tajawal'),
        bodyLarge: TextStyle(fontFamily: 'Tajawal'),
        bodyMedium: TextStyle(fontFamily: 'Tajawal'),
        bodySmall: TextStyle(fontFamily: 'Tajawal'),
      ),
    );
  }
}

// ======================================================
// أدوات مساعدة (Utilities)
// ======================================================

class AppUtils {
  // التحقق من صحة البريد الإلكتروني
  static bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }
  
  // عرض رسالة للمستخدم
  static void showSnackBar(BuildContext context, String message, {bool isError = false}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message, style: const TextStyle(fontFamily: 'Tajawal')),
        backgroundColor: isError ? AppColors.errorColor : AppColors.primaryColor,
        duration: const Duration(seconds: 3),
      ),
    );
  }
  
  // عرض مربع حوار للتأكيد
  static Future<bool> showConfirmDialog(
    BuildContext context, {
    required String title,
    required String content,
  }) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title, style: const TextStyle(fontFamily: 'Tajawal')),
        content: Text(content, style: const TextStyle(fontFamily: 'Tajawal')),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء', style: TextStyle(fontFamily: 'Tajawal')),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('تأكيد', style: TextStyle(fontFamily: 'Tajawal', color: AppColors.errorColor)),
          ),
        ],
      ),
    );
    return result ?? false;
  }
  
  // اختيار صورة من الكاميرا أو المعرض
  static Future<File?> pickImage(ImageSource source) async {
    final picker = ImagePicker();
    final pickedFile = await picker.pickImage(source: source);
    if (pickedFile != null) {
      return File(pickedFile.path);
    }
    return null;
  }
  
  // تنسيق التاريخ والوقت
  static String formatDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    
    if (difference.inDays == 0) {
      return 'اليوم ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays == 1) {
      return 'الأمس ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else {
      return '${dateTime.day}/${dateTime.month}/${dateTime.year}';
    }
  }
  
  // تنسيق الوقت
  static String formatTime(DateTime time) {
    return '${time.hour}:${time.minute.toString().padLeft(2, '0')}';
  }
  
  // تنسيق وقت المحادثة
  static String formatChatTime(DateTime time) {
    final now = DateTime.now();
    final difference = now.difference(time);
    
    if (difference.inMinutes < 1) {
      return 'الآن';
    } else if (difference.inHours < 1) {
      return 'منذ ${difference.inMinutes} دقيقة';
    } else if (difference.inDays < 1) {
      return '${time.hour}:${time.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays == 1) {
      return 'الأمس';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} أيام';
    } else {
      return '${time.day}/${time.month}';
    }
  }
}
