// ملف الثوابت والمظهر والألوان والأدوات المساعدة

import 'package:flutter/material.dart';

// الألوان الرئيسية للتطبيق
class AppColors {
  static const Color primary = Color(0xFF2E7D32); // اللون الأخضر الداكن
  static const Color secondary = Color(0xFF81C784); // اللون الأخضر الفاتح
  static const Color accent = Color(0xFFFFC107); // اللون الأصفر
  static const Color background = Color(0xFFF5F5F5); // لون الخلفية
  static const Color text = Color(0xFF212121); // لون النص الأساسي
  static const Color textSecondary = Color(0xFF757575); // لون النص الثانوي
  static const Color error = Color(0xFFD32F2F); // لون الخطأ
  static const Color success = Color(0xFF388E3C); // لون النجاح
  static const Color warning = Color(0xFFFFA000); // لون التحذير
  static const Color divider = Color(0xFFBDBDBD); // لون الفاصل
}

// ثوابت التطبيق
class AppConstants {
  // عنوان التطبيق
  static const String appName = "SAM - مساعدك ومرشدك الزراعي اليمني الذكي";
  
  // النصوص الثابتة
  static const String welcomeMessage = "مرحباً بك في تطبيق SAM، مساعدك ومرشدك الزراعي اليمني الذكي";
  static const String loginTitle = "تسجيل الدخول";
  static const String registerTitle = "إنشاء حساب جديد";
  static const String forgotPasswordTitle = "استعادة كلمة المرور";
  static const String homeTitle = "الرئيسية";
  static const String irrigationTitle = "نظام الري الذكي";
  static const String diseasesTitle = "تشخيص أمراض النباتات";
  static const String marketTitle = "السوق الزراعي";
  static const String chatTitle = "المحادثات";
  static const String profileTitle = "الملف الشخصي";
  static const String settingsTitle = "الإعدادات";
  
  // رسائل الخطأ
  static const String emailRequired = "البريد الإلكتروني مطلوب";
  static const String invalidEmail = "البريد الإلكتروني غير صالح";
  static const String passwordRequired = "كلمة المرور مطلوبة";
  static const String passwordTooShort = "كلمة المرور قصيرة جداً (6 أحرف على الأقل)";
  static const String nameRequired = "الاسم مطلوب";
  static const String phoneRequired = "رقم الهاتف مطلوب";
  static const String invalidPhone = "رقم الهاتف غير صالح";
  static const String fieldRequired = "هذا الحقل مطلوب";
  
  // رسائل النجاح
  static const String loginSuccess = "تم تسجيل الدخول بنجاح";
  static const String registerSuccess = "تم إنشاء الحساب بنجاح";
  static const String passwordResetSuccess = "تم إرسال رابط استعادة كلمة المرور إلى بريدك الإلكتروني";
  static const String profileUpdateSuccess = "تم تحديث الملف الشخصي بنجاح";
  static const String irrigationUpdateSuccess = "تم تحديث إعدادات نظام الري بنجاح";
  static const String productAddSuccess = "تم إضافة المنتج بنجاح";
  
  // أزرار
  static const String loginButton = "تسجيل الدخول";
  static const String registerButton = "إنشاء حساب";
  static const String forgotPasswordButton = "استعادة كلمة المرور";
  static const String saveButton = "حفظ";
  static const String cancelButton = "إلغاء";
  static const String updateButton = "تحديث";
  static const String addButton = "إضافة";
  static const String deleteButton = "حذف";
  static const String logoutButton = "تسجيل الخروج";
  static const String sendButton = "إرسال";
  static const String captureButton = "التقاط صورة";
  static const String galleryButton = "اختيار من المعرض";
  static const String analyzeButton = "تحليل الصورة";
  
  // روابط الصور
  static const String logoPath = "assets/images/logo.png";
  static const String defaultAvatarPath = "assets/images/default_avatar.png";
  static const String placeholderImagePath = "assets/images/placeholder.png";
  
  // قيم افتراضية
  static const int defaultSoilMoistureThreshold = 30; // نسبة رطوبة التربة الافتراضية للتشغيل التلقائي
  static const int defaultWaterLevelWarning = 20; // نسبة مستوى الماء للتحذير
  static const int defaultIrrigationDuration = 10; // مدة الري الافتراضية بالدقائق
}

// موضوع التطبيق
class AppTheme {
  // موضوع التطبيق الرئيسي
  static ThemeData get lightTheme {
    return ThemeData(
      primaryColor: AppColors.primary,
      colorScheme: ColorScheme.light(
        primary: AppColors.primary,
        secondary: AppColors.secondary,
        background: AppColors.background,
        error: AppColors.error,
      ),
      scaffoldBackgroundColor: AppColors.background,
      appBarTheme: const AppBarTheme(
        backgroundColor: AppColors.primary,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          color: Colors.white,
          fontSize: 20,
          fontWeight: FontWeight.bold,
        ),
        iconTheme: IconThemeData(color: Colors.white),
      ),
      textTheme: const TextTheme(
        displayLarge: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: AppColors.text,
        ),
        displayMedium: TextStyle(
          fontSize: 22,
          fontWeight: FontWeight.bold,
          color: AppColors.text,
        ),
        displaySmall: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: AppColors.text,
        ),
        headlineMedium: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppColors.text,
        ),
        headlineSmall: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: AppColors.text,
        ),
        titleLarge: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: AppColors.text,
        ),
        bodyLarge: TextStyle(
          fontSize: 16,
          color: AppColors.text,
        ),
        bodyMedium: TextStyle(
          fontSize: 14,
          color: AppColors.text,
        ),
        bodySmall: TextStyle(
          fontSize: 12,
          color: AppColors.textSecondary,
        ),
      ),
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.primary,
          textStyle: const TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      outlinedButtonTheme: OutlinedButtonThemeData(
        style: OutlinedButton.styleFrom(
          foregroundColor: AppColors.primary,
          side: const BorderSide(color: AppColors.primary),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(10),
          ),
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
          textStyle: const TextStyle(
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: Colors.white,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(color: AppColors.divider),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(color: AppColors.divider),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(color: AppColors.primary, width: 2),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(10),
          borderSide: const BorderSide(color: AppColors.error),
        ),
        contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
        hintStyle: const TextStyle(color: AppColors.textSecondary),
        errorStyle: const TextStyle(color: AppColors.error),
      ),
      cardTheme: CardTheme(
        color: Colors.white,
        elevation: 2,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
        ),
        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 0),
      ),
      dividerTheme: const DividerThemeData(
        color: AppColors.divider,
        thickness: 1,
        space: 16,
      ),
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        backgroundColor: Colors.white,
        selectedItemColor: AppColors.primary,
        unselectedItemColor: AppColors.textSecondary,
        selectedLabelStyle: TextStyle(fontSize: 12),
        unselectedLabelStyle: TextStyle(fontSize: 12),
        showUnselectedLabels: true,
        type: BottomNavigationBarType.fixed,
        elevation: 8,
      ),
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      progressIndicatorTheme: const ProgressIndicatorThemeData(
        color: AppColors.primary,
      ),
      checkboxTheme: CheckboxThemeData(
        fillColor: MaterialStateProperty.resolveWith<Color>((states) {
          if (states.contains(MaterialState.selected)) {
            return AppColors.primary;
          }
          return Colors.transparent;
        }),
        side: const BorderSide(color: AppColors.primary),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(4),
        ),
      ),
      radioTheme: RadioThemeData(
        fillColor: MaterialStateProperty.resolveWith<Color>((states) {
          if (states.contains(MaterialState.selected)) {
            return AppColors.primary;
          }
          return AppColors.textSecondary;
        }),
      ),
      switchTheme: SwitchThemeData(
        thumbColor: MaterialStateProperty.resolveWith<Color>((states) {
          if (states.contains(MaterialState.selected)) {
            return AppColors.primary;
          }
          return Colors.white;
        }),
        trackColor: MaterialStateProperty.resolveWith<Color>((states) {
          if (states.contains(MaterialState.selected)) {
            return AppColors.primary.withOpacity(0.5);
          }
          return AppColors.divider;
        }),
      ),
      tabBarTheme: const TabBarTheme(
        labelColor: AppColors.primary,
        unselectedLabelColor: AppColors.textSecondary,
        indicatorSize: TabBarIndicatorSize.tab,
        labelStyle: TextStyle(fontSize: 14, fontWeight: FontWeight.bold),
        unselectedLabelStyle: TextStyle(fontSize: 14),
      ),
      dialogTheme: DialogTheme(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(15),
        ),
        backgroundColor: Colors.white,
        elevation: 5,
      ),
      snackBarTheme: SnackBarThemeData(
        backgroundColor: AppColors.text,
        contentTextStyle: const TextStyle(color: Colors.white),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        behavior: SnackBarBehavior.floating,
      ),
      fontFamily: 'Cairo', // خط عربي مناسب
    );
  }
}

// أدوات مساعدة للتحقق من صحة المدخلات
class Validators {
  // التحقق من صحة البريد الإلكتروني
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return AppConstants.emailRequired;
    }
    
    final emailRegex = RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$');
    if (!emailRegex.hasMatch(value)) {
      return AppConstants.invalidEmail;
    }
    
    return null;
  }
  
  // التحقق من صحة كلمة المرور
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return AppConstants.passwordRequired;
    }
    
    if (value.length < 6) {
      return AppConstants.passwordTooShort;
    }
    
    return null;
  }
  
  // التحقق من صحة الاسم
  static String? validateName(String? value) {
    if (value == null || value.isEmpty) {
      return AppConstants.nameRequired;
    }
    
    return null;
  }
  
  // التحقق من صحة رقم الهاتف
  static String? validatePhone(String? value) {
    if (value == null || value.isEmpty) {
      return AppConstants.phoneRequired;
    }
    
    final phoneRegex = RegExp(r'^\d{9}$'); // رقم هاتف يمني (9 أرقام)
    if (!phoneRegex.hasMatch(value)) {
      return AppConstants.invalidPhone;
    }
    
    return null;
  }
  
  // التحقق من أن الحقل غير فارغ
  static String? validateRequired(String? value) {
    if (value == null || value.isEmpty) {
      return AppConstants.fieldRequired;
    }
    
    return null;
  }
}

// أدوات مساعدة للتنسيق
class Formatters {
  // تنسيق التاريخ
  static String formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
  
  // تنسيق الوقت
  static String formatTime(DateTime time) {
    return '${time.hour}:${time.minute.toString().padLeft(2, '0')}';
  }
  
  // تنسيق التاريخ والوقت
  static String formatDateTime(DateTime dateTime) {
    return '${formatDate(dateTime)} ${formatTime(dateTime)}';
  }
  
  // تنسيق السعر
  static String formatPrice(double price) {
    return '$price ريال';
  }
  
  // تنسيق النسبة المئوية
  static String formatPercentage(double percentage) {
    return '${(percentage * 100).toStringAsFixed(1)}%';
  }
}

// أدوات مساعدة للتنقل
class NavigationHelper {
  // الانتقال إلى صفحة جديدة
  static void navigateTo(BuildContext context, String routeName, {Object? arguments}) {
    Navigator.pushNamed(context, routeName, arguments: arguments);
  }
  
  // الانتقال إلى صفحة جديدة واستبدال الصفحة الحالية
  static void navigateToReplacement(BuildContext context, String routeName, {Object? arguments}) {
    Navigator.pushReplacementNamed(context, routeName, arguments: arguments);
  }
  
  // الانتقال إلى الصفحة الرئيسية وحذف كل الصفحات السابقة
  static void navigateToHome(BuildContext context) {
    Navigator.pushNamedAndRemoveUntil(context, '/home', (route) => false);
  }
  
  // العودة إلى الصفحة السابقة
  static void goBack(BuildContext context) {
    Navigator.pop(context);
  }
}

// أدوات مساعدة للإشعارات
class NotificationHelper {
  // عرض إشعار نجاح
  static void showSuccessSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.success,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
      ),
    );
  }
  
  // عرض إشعار خطأ
  static void showErrorSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
      ),
    );
  }
  
  // عرض إشعار تحذير
  static void showWarningSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.warning,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
      ),
    );
  }
  
  // عرض إشعار معلومات
  static void showInfoSnackBar(BuildContext context, String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.primary,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
      ),
    );
  }
  
  // عرض حوار تأكيد
  static Future<bool> showConfirmationDialog(
    BuildContext context,
    String title,
    String message,
  ) async {
    final result = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(message),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text(AppConstants.cancelButton),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text(AppConstants.saveButton),
          ),
        ],
      ),
    );
    
    return result ?? false;
  }
}

// أدوات مساعدة للصور
class ImageHelper {
  // تحميل صورة من المعرض
  static Future<String?> pickImageFromGallery() async {
    // هذه مجرد محاكاة، في التطبيق الحقيقي سيتم استخدام image_picker
    await Future.delayed(const Duration(seconds: 1));
    return 'assets/images/default_avatar.png';
  }
  
  // التقاط صورة من الكاميرا
  static Future<String?> captureImageFromCamera() async {
    // هذه مجرد محاكاة، في التطبيق الحقيقي سيتم استخدام image_picker
    await Future.delayed(const Duration(seconds: 1));
    return 'assets/images/default_avatar.png';
  }
}

// أدوات مساعدة للتخزين المحلي
class StorageHelper {
  // حفظ قيمة في التخزين المحلي
  static Future<void> saveValue(String key, String value) async {
    // هذه مجرد محاكاة، في التطبيق الحقيقي سيتم استخدام shared_preferences
    await Future.delayed(const Duration(milliseconds: 100));
  }
  
  // قراءة قيمة من التخزين المحلي
  static Future<String?> getValue(String key) async {
    // هذه مجرد محاكاة، في التطبيق الحقيقي سيتم استخدام shared_preferences
    await Future.delayed(const Duration(milliseconds: 100));
    return null;
  }
  
  // حذف قيمة من التخزين المحلي
  static Future<void> removeValue(String key) async {
    // هذه مجرد محاكاة، في التطبيق الحقيقي سيتم استخدام shared_preferences
    await Future.delayed(const Duration(milliseconds: 100));
  }
  
  // حذف جميع القيم من التخزين المحلي
  static Future<void> clearAll() async {
    // هذه مجرد محاكاة، في التطبيق الحقيقي سيتم استخدام shared_preferences
    await Future.delayed(const Duration(milliseconds: 100));
  }
}
