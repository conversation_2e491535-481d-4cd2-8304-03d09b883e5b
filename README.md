// ملف README.md - دليل تشغيل تطبيق SAM (مساعدك ومرشدك الزراعي اليمني الذكي)

# تطبيق SAM - مساعدك ومرشدك الزراعي اليمني الذكي

## نظرة عامة
تطبيق SAM هو تطبيق زراعي ذكي يهدف إلى مساعدة المزارعين اليمنيين في إدارة أنظمة الري الذكية، تشخيص أمراض النباتات باستخدام الذكاء الاصطناعي، والتواصل مع مزارعين آخرين من خلال السوق الزراعي والمحادثات.

## متطلبات التشغيل
- Flutter SDK (أحدث إصدار)
- Dart SDK (أحدث إصدار)
- Android Studio أو VS Code
- جهاز Android أو iOS أو محاكي

## خطوات التشغيل
1. قم بفك ضغط الملف المضغوط `sam_app_minimal.zip`
2. افتح المجلد في Android Studio أو VS Code
3. قم بتشغيل الأمر التالي لتثبيت المكتبات المطلوبة:
   ```
   flutter pub get
   ```
4. قم بتشغيل التطبيق على جهازك أو المحاكي باستخدام الأمر:
   ```
   flutter run
   ```

## هيكل المشروع
المشروع مقسم إلى عدد قليل من الملفات لسهولة الفهم والتعديل:

- `main.dart`: نقطة بداية التطبيق وإعداد Firebase
- `models.dart`: نماذج البيانات المستخدمة في التطبيق
- `services.dart`: خدمات التطبيق مثل المصادقة وقاعدة البيانات
- `screens.dart`: جميع شاشات التطبيق
- `widgets.dart`: الويدجت المشتركة المستخدمة في التطبيق
- `utils.dart`: الثوابت والأدوات المساعدة

## الميزات الرئيسية
1. **نظام المصادقة**: تسجيل الدخول، إنشاء حساب، استعادة كلمة المرور
2. **الشاشة الرئيسية**: عرض ملخص لجميع الأنظمة والإحصائيات
3. **نظام الري الذكي**: التحكم في أنظمة الري ومراقبة الحساسات
4. **تشخيص أمراض النباتات**: تحليل صور النباتات وتشخيص الأمراض
5. **السوق الزراعي**: منصة لبيع وشراء المنتجات الزراعية
6. **المحادثات**: التواصل مع المزارعين الآخرين
7. **الملف الشخصي**: إدارة معلومات المستخدم
8. **الإعدادات**: تخصيص إعدادات التطبيق

## حسابات تجريبية
يمكنك استخدام الحسابات التالية للدخول إلى التطبيق:

- **البريد الإلكتروني**: <EMAIL>
- **كلمة المرور**: 123456

## ملاحظات هامة
- التطبيق يعمل في وضع المحاكاة ولا يحتاج إلى اتصال فعلي بـ Firebase
- جميع البيانات المعروضة هي بيانات تجريبية لأغراض العرض
- لاستخدام التطبيق في بيئة الإنتاج، يجب إعداد مشروع Firebase وربطه بالتطبيق

## الدعم والمساعدة
إذا واجهتك أي مشكلة في تشغيل التطبيق، يرجى التواصل معنا على البريد الإلكتروني: <EMAIL>
